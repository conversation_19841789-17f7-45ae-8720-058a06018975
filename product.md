# Crie um projeto backend em Golang com a seguinte estrutura e funcionalidades:

# ApexStream

## 📦 Objetivo:

Construir um serviço de backend para processar vídeos enviados por usuários para uma plataforma de vídeo aulas. O sistema deve permitir upload, processamento com FFmpeg, geração de arquivos HLS (.m3u8 + .ts) e upload final para o Cloudflare R2.

## 🔧 Tecnologias:

- Golang
- Gin para a API REST
- FFmpeg (executado via os/exec)
- Docker
- Cloudflare R2 - (compatível com Amazon S3, usar SDK compatível)
- Postgres - Verificar necessidade (opcional, apenas se necessário para salvar metadados)

## 🛠️ Funcionalidades mínimas:

1. Endpoint `POST /upload` para fazer upload de um vídeo (`.mp4`)
2. Após upload, iniciar o processamento do vídeo com FFmpeg:
   - Converter para HLS (streaming adaptativo): gerar `.m3u8` e segmentos `.ts`
3. Após processamento, fazer upload dos arquivos gerados para o Cloudflare R2
4. Retornar para o cliente a URL do arquivo `.m3u8` hospedado no R2
5. Criar logs e tratamento de erro robusto durante todo o processo

## 📁 Estrutura de pastas sugerida:
- `cmd/` → entrypoint
- `internal/api/` → rotas HTTP
- `internal/processor/` → chamada e controle do FFmpeg
- `internal/storage/` → integração com R2
- `videos/` → pasta temporária para vídeos locais

## 📌 Observações:
- Utilizar variáveis de ambiente para credenciais do R2
- Implementar controle de concorrência com goroutines
- Código comentado e modularizado
- Sem UI, apenas API REST

## 🎯 Futuro:
Projeto deve estar pronto para adicionar:
- Controle de autenticação
- Fila de processamento com Redis ou canal Go
- Armazenamento dos metadados no Postgres

## 🚀 Todo List - Projeto Backend Video Processing

### 📋 Setup Inicial
- [x] Inicializar módulo Go (`go mod init apexstrem`)
- [x] Criar estrutura de pastas:
  - [x] `cmd/server/`
  - [x] `internal/api/`
  - [x] `internal/processor/`
  - [x] `internal/storage/`
  - [x] `videos/` (pasta temporária)
- [x] Instalar dependências:
  - [x] Gin (`go get github.com/gin-gonic/gin`)
  - [x] AWS SDK para R2 (`go get github.com/aws/aws-sdk-go-v2`)
- [x] Criar `.env` para variáveis de ambiente
- [x] Criar `.gitignore` (ignorar `videos/` e `.env`)

### 🔧 Desenvolvimento Core
- [x] **cmd/server/main.go** - Entry point da aplicação
- [x] **internal/api/handlers.go** - Handler para `POST /upload`
- [x] **internal/api/routes.go** - Setup das rotas Gin
- [x] **internal/processor/ffmpeg.go** - Wrapper para FFmpeg
- [x] **internal/storage/r2.go** - Integração Cloudflare R2

### 🎯 Funcionalidades MVP
- [x] Endpoint `POST /upload` recebendo arquivo `.mp4`
- [x] Validação de arquivo (tipo, tamanho)
- [x] Processamento FFmpeg (MP4 → HLS)
- [x] Geração de `.m3u8` e segmentos `.ts`
- [x] Storage local dos arquivos processados
- [x] Retorno da URL do `.m3u8`
- [x] Logs estruturados
- [x] Tratamento de erros robusto

### 🧪 Testes & Deploy
- [x] Dockerfile para containerização
- [x] docker-compose.yml (opcional)
- [ ] Testes unitários básicos
- [ ] README.md com instruções
- [ ] Verificar FFmpeg instalado no container

### 🔮 Preparação Futuro
- [x] Interface para storage (local/R2)
- [x] Estrutura para autenticação
- [ ] Preparar para fila de processamento
- [ ] Schema Postgres (comentado)

