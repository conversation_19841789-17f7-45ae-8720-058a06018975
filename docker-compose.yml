services:
  redis:
    image: redis:7-alpine
    container_name: apexstream-redis
    ports:
      - "6379:6379"
    volumes:
      - apexstream_redis_data:/data
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 3
      start_period: 10s
    networks:
      - apexstream-network

  apexstream:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: apexstream-server
    ports:
      - "8090:8090"
    depends_on:
      redis:
        condition: service_healthy
    environment:
      # Server Configuration
      - PORT=${PORT:-8090}
      - ENVIRONMENT=${ENVIRONMENT:-production}

      # File Processing
      - VIDEO_DIR=/app/videos
      - MAX_FILE_SIZE=${MAX_FILE_SIZE:-104857600}
      - ENABLE_FFMPEG=${ENABLE_FFMPEG:-true}

      # Storage Configuration
      - ENABLE_STORAGE=${ENABLE_STORAGE:-true}
      - STORAGE_TYPE=${STORAGE_TYPE:-local}

      # Authentication
      - ENABLE_AUTH=${ENABLE_AUTH:-false}
      - API_KEYS=${API_KEYS:-your-secret-api-key-1,your-secret-api-key-2}

      # Redis Configuration
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD:-}
      - REDIS_DB=${REDIS_DB:-0}
      - REDIS_POOL_SIZE=${REDIS_POOL_SIZE:-10}

      # Worker Pool Configuration
      - WORKER_POOL_SIZE=${WORKER_POOL_SIZE:-3}
      - JOB_TIMEOUT=${JOB_TIMEOUT:-300s}
      - MAX_RETRIES=${MAX_RETRIES:-3}
      - RETRY_DELAY=${RETRY_DELAY:-30s}

      # Queue Configuration
      - ENABLE_QUEUE=${ENABLE_QUEUE:-true}
      - QUEUE_CLEANUP_INTERVAL=${QUEUE_CLEANUP_INTERVAL:-60s}
      
      # Local Storage (default)
      - LOCAL_STORAGE_DIR=/app/storage
      - LOCAL_STORAGE_URL=/files
      
      # R2 Storage (uncomment and configure if using Cloudflare R2)
      - R2_ENDPOINT=${R2_ENDPOINT}
      - R2_ACCESS_KEY=${R2_ACCESS_KEY}
      - R2_SECRET_KEY=${R2_SECRET_KEY}
      - R2_BUCKET_NAME=${R2_BUCKET_NAME}
      - R2_PUBLIC_URL=${R2_PUBLIC_URL}
    volumes:
      # Persist video files and processed content
      - apexstream_videos:/app/videos
      - apexstream_storage:/app/storage
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8090/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - apexstream-network

volumes:
  apexstream_videos:
    driver: local
  apexstream_storage:
    driver: local
  apexstream_redis_data:
    driver: local

networks:
  apexstream-network:
    driver: bridge
