# ApexStream API Documentation

## Visão Geral

O ApexStream é um sistema de processamento de vídeo que converte vídeos para formato HLS (HTTP Live Streaming) em múltiplas qualidades (480p, 720p, 1080p) de forma assíncrona, com progresso em tempo real via WebSocket.

## Modos de Operação

O ApexStream opera em dois modos diferentes, dependendo da configuração:

### 1. Sistema com Fila (Recomendado)
- **Habilitado quando**: `ENABLE_QUEUE=true` (padrão)
- **Características**:
  - Processamento assíncrono com Redis
  - Pool de workers configurável
  - Monitoramento avançado via WebSocket
  - Retry automático de jobs falhados
  - Estatísticas detalhadas de fila e workers
- **Endpoints principais**: `/upload`, `/jobs/{id}`, `/jobs`, `/queue/stats`, `/workers/stats`

### 2. Sistema Tradicional (Fallback)
- **Habilitado quando**: `ENABLE_QUEUE=false`
- **Características**:
  - Processamento síncrono simples
  - Sem dependência do Redis
  - Status básico de processamento
  - Ideal para desenvolvimento ou ambientes simples
- **Endpoints principais**: `/upload`, `/status/{id}`

> **Nota**: A documentação abaixo cobre ambos os modos. Os endpoints específicos do sistema com fila são claramente marcados.

## Base URL

```
http://localhost:8080/api/v1
```

## Autenticação

A API utiliza autenticação via API Key no header:

```
Authorization: Bearer YOUR_API_KEY
```

## Endpoints

### Endpoints Básicos do Sistema

#### `GET /`

Informações gerais da API.

**Resposta de Sucesso (200):**
```json
{
  "service": "ApexStream Video Processing API",
  "version": "1.0.0",
  "status": "running",
  "timestamp": **********,
  "health": "/health",
  "api": "/api/v1"
}
```

#### `GET /health`

Health check básico do sistema.

**Resposta de Sucesso (200):**
```json
{
  "status": "ok",
  "timestamp": **********,
  "service": "apexstream"
}
```

### 1. Upload e Processamento de Vídeo

#### `POST /upload`

Faz upload de um vídeo e inicia o processamento em múltiplas qualidades.

**Headers:**
```
Content-Type: multipart/form-data
Authorization: Bearer YOUR_API_KEY
```

**Body (form-data):**
- `video` (file): Arquivo de vídeo
- `quality_config` (string, opcional): JSON com configurações de qualidade

**Exemplo de quality_config:**
```json
{
  "process_in_parallel": true,
  "qualities": [
    {
      "name": "480p",
      "width": 854,
      "height": 480,
      "bitrate": 1000,
      "audio_rate": 128,
      "max_rate": 1200,
      "buf_size": 2000
    },
    {
      "name": "720p", 
      "width": 1280,
      "height": 720,
      "bitrate": 2500,
      "audio_rate": 128,
      "max_rate": 3000,
      "buf_size": 5000
    },
    {
      "name": "1080p",
      "width": 1920,
      "height": 1080,
      "bitrate": 5000,
      "audio_rate": 192,
      "max_rate": 6000,
      "buf_size": 10000
    }
  ]
}
```

**Resposta de Sucesso (202):**
```json
{
  "status": "success",
  "message": "Vídeo enviado para processamento",
  "job_id": "job_abc123def456",
  "websocket_url": "ws://localhost:8080/api/v1/ws/job_abc123def456"
}
```

### 2. Status do Job

#### `GET /jobs/{job_id}` (Sistema com Fila) ou `GET /status/{id}` (Sistema Tradicional)

Obtém o status atual de um job de processamento.

**Headers:**
```
Authorization: Bearer YOUR_API_KEY
```

**Resposta de Sucesso (200):**
```json
{
  "status": "success",
  "data": {
    "id": "job_abc123def456",
    "status": "processing",
    "progress": 65.5,
    "current_quality": "720p",
    "quality_results": [
      {
        "quality": "480p",
        "success": true,
        "progress": 100,
        "status": "completed",
        "playlist_path": "/videos/processed/job_abc123def456/480p/playlist.m3u8",
        "segment_count": 45,
        "file_size": 15728640
      },
      {
        "quality": "720p",
        "success": false,
        "progress": 75,
        "status": "processing",
        "message": "Processando segmento 30/40"
      },
      {
        "quality": "1080p",
        "success": false,
        "progress": 0,
        "status": "pending",
        "message": "Aguardando processamento"
      }
    ],
    "master_playlist_path": "/videos/processed/job_abc123def456/master.m3u8",
    "duration": 180.5,
    "created_at": "2024-01-15T10:30:00Z",
    "started_at": "2024-01-15T10:30:05Z"
  }
}
```

### 3. Listar Jobs (Sistema com Fila)

#### `GET /jobs`

Lista todos os jobs de processamento.

**Headers:**
```
Authorization: Bearer YOUR_API_KEY
```

**Resposta de Sucesso (200):**
```json
{
  "status": "success",
  "data": {
    "jobs": [
      {
        "id": "job_abc123def456",
        "status": "completed",
        "progress": 100,
        "created_at": "2024-01-15T10:30:00Z",
        "completed_at": "2024-01-15T10:35:00Z"
      }
    ],
    "total": 1,
    "page": 1,
    "per_page": 10
  }
}
```

### 4. Estatísticas da Fila (Sistema com Fila)

#### `GET /queue/stats`

Obtém estatísticas da fila de processamento.

**Headers:**
```
Authorization: Bearer YOUR_API_KEY
```

**Resposta de Sucesso (200):**
```json
{
  "status": "success",
  "data": {
    "pending_jobs": 5,
    "processing_jobs": 2,
    "completed_jobs": 150,
    "failed_jobs": 3,
    "total_jobs": 160
  }
}
```

### 5. Reprocessar Job (Sistema com Fila)

#### `POST /jobs/{job_id}/retry`

Reprocessa um job que falhou.

**Headers:**
```
Authorization: Bearer YOUR_API_KEY
```

**Resposta de Sucesso (200):**
```json
{
  "status": "success",
  "message": "Job reenviado para processamento",
  "job_id": "job_abc123def456"
}
```

### 6. Estatísticas dos Workers (Sistema com Fila)

#### `GET /workers/stats`

Obtém estatísticas dos workers de processamento.

**Headers:**
```
Authorization: Bearer YOUR_API_KEY
```

**Resposta de Sucesso (200):**
```json
{
  "status": "success",
  "data": {
    "active_workers": 3,
    "idle_workers": 1,
    "total_workers": 4,
    "jobs_processed": 150,
    "average_processing_time": 45.5
  }
}
```

### 7. Limpeza da Fila (Sistema com Fila)

#### `POST /queue/cleanup`

Remove jobs antigos e limpa a fila.

**Headers:**
```
Authorization: Bearer YOUR_API_KEY
```

**Resposta de Sucesso (200):**
```json
{
  "status": "success",
  "message": "Limpeza da fila concluída",
  "removed_jobs": 25
}
```

### 8. Purgar Fila (Sistema com Fila)

#### `DELETE /queue/{queue_name}/purge`

Remove todos os jobs de uma fila específica.

**Headers:**
```
Authorization: Bearer YOUR_API_KEY
```

**Resposta de Sucesso (200):**
```json
{
  "status": "success",
  "message": "Fila purgada com sucesso",
  "queue": "video_processing",
  "removed_jobs": 10
}
```

### 9. Health Check da Fila (Sistema com Fila)

#### `GET /health/queue`

Verifica a saúde do sistema de filas.

**Resposta de Sucesso (200):**
```json
{
  "status": "healthy",
  "queue": "connected",
  "workers": "active",
  "redis": "connected"
}
```

### 10. WebSocket para Progresso em Tempo Real

#### `WS /ws/{job_id}`

Conecta ao WebSocket para receber atualizações de progresso em tempo real.

**URL:**
```
ws://localhost:8080/api/v1/ws/job_abc123def456?user_id=optional_user_id
```

**Mensagens Recebidas:**

**Conexão estabelecida:**
```json
{
  "type": "connected",
  "job_id": "job_abc123def456",
  "timestamp": "2024-01-15T10:30:00Z",
  "message": "Conectado ao sistema de progresso"
}
```

**Progresso de processamento:**
```json
{
  "type": "progress",
  "job_id": "job_abc123def456",
  "progress": 45.5,
  "status": "processing",
  "message": "Processando múltiplas qualidades",
  "current_quality": "720p",
  "quality_progress": [
    {
      "quality": "480p",
      "progress": 100,
      "status": "completed",
      "message": "Processamento concluído",
      "started_at": "2024-01-15T10:30:05Z",
      "completed_at": "2024-01-15T10:32:15Z"
    },
    {
      "quality": "720p",
      "progress": 65,
      "status": "processing",
      "message": "Processando segmento 26/40",
      "started_at": "2024-01-15T10:30:05Z"
    },
    {
      "quality": "1080p",
      "progress": 0,
      "status": "pending",
      "message": "Aguardando processamento"
    }
  ],
  "timestamp": "2024-01-15T10:32:30Z"
}
```

**Job concluído:**
```json
{
  "type": "progress",
  "job_id": "job_abc123def456",
  "progress": 100,
  "status": "completed",
  "message": "Processamento concluído com sucesso",
  "data": {
    "duration": 180.5,
    "total_segments": 135,
    "master_playlist_path": "/videos/processed/job_abc123def456/master.m3u8",
    "qualities_processed": ["480p", "720p", "1080p"]
  },
  "timestamp": "2024-01-15T10:35:00Z"
}
```

### 11. Estatísticas do WebSocket

#### `GET /ws/stats`

Obtém estatísticas das conexões WebSocket.

**Headers:**
```
Authorization: Bearer YOUR_API_KEY
```

**Resposta de Sucesso (200):**
```json
{
  "status": "success",
  "data": {
    "total_clients": 15,
    "jobs_monitored": 8,
    "active_connections": 12
  }
}
```

### 12. Health Check do WebSocket

#### `GET /ws/health`

Verifica a saúde do sistema WebSocket.

**Resposta de Sucesso (200):**
```json
{
  "status": "healthy",
  "websocket": "active",
  "total_clients": 15,
  "jobs_monitored": 8
}
```

### 13. Broadcast de Mensagem (Admin)

#### `POST /ws/broadcast`

Envia uma mensagem para todos os clientes conectados.

**Headers:**
```
Authorization: Bearer YOUR_API_KEY
Content-Type: application/json
```

**Body:**
```json
{
  "type": "announcement",
  "message": "Sistema será reiniciado em 5 minutos",
  "data": {
    "scheduled_time": "2024-01-15T15:00:00Z"
  }
}
```

### 14. Enviar Progresso Manual

#### `POST /ws/{job_id}/progress`

Envia atualização de progresso manual para um job específico.

**Headers:**
```
Authorization: Bearer YOUR_API_KEY
Content-Type: application/json
```

**Body:**
```json
{
  "progress": 75.5,
  "status": "processing",
  "message": "Processando qualidade 720p",
  "data": {
    "current_quality": "720p",
    "estimated_time": 120
  }
}
```

## Estados do Job

- `pending`: Job na fila aguardando processamento
- `processing`: Vídeo sendo processado
- `uploading`: Arquivos sendo enviados para storage (se habilitado)
- `completed`: Processamento concluído com sucesso
- `failed`: Processamento falhou
- `retrying`: Tentando reprocessar após falha

## Estados das Qualidades

- `pending`: Qualidade aguardando processamento
- `processing`: Qualidade sendo processada
- `completed`: Qualidade processada com sucesso
- `failed`: Falha no processamento da qualidade

## Códigos de Erro

- `400`: Requisição inválida
- `401`: Não autorizado (API key inválida)
- `404`: Recurso não encontrado
- `413`: Arquivo muito grande
- `422`: Dados inválidos
- `500`: Erro interno do servidor

## Exemplos de Uso

### Sistema com Fila (Recomendado)

```javascript
// 1. Upload do vídeo
const formData = new FormData();
formData.append('video', videoFile);
formData.append('quality_config', JSON.stringify({
  process_in_parallel: true,
  qualities: [
    { name: "480p", width: 854, height: 480, bitrate: 1000, audio_rate: 128, max_rate: 1200, buf_size: 2000 },
    { name: "720p", width: 1280, height: 720, bitrate: 2500, audio_rate: 128, max_rate: 3000, buf_size: 5000 },
    { name: "1080p", width: 1920, height: 1080, bitrate: 5000, audio_rate: 192, max_rate: 6000, buf_size: 10000 }
  ]
}));

const response = await fetch('/api/v1/upload', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer YOUR_API_KEY'
  },
  body: formData
});

const result = await response.json();
const jobId = result.job_id;

// 2. Conectar ao WebSocket para progresso em tempo real
const ws = new WebSocket(`ws://localhost:8080/api/v1/ws/${jobId}`);

ws.onmessage = (event) => {
  const data = JSON.parse(event.data);
  console.log('Progresso:', data);

  if (data.type === 'progress') {
    updateProgressUI(data.progress, data.quality_progress);
  }
};

// 3. Verificar status via API (alternativa ao WebSocket)
const statusResponse = await fetch(`/api/v1/jobs/${jobId}`, {
  headers: { 'Authorization': 'Bearer YOUR_API_KEY' }
});
const status = await statusResponse.json();

// 4. Quando concluído, usar a playlist master
// http://localhost:8080/api/v1/videos/{jobId}/master.m3u8
```

### Sistema Tradicional

```javascript
// 1. Upload do vídeo
const formData = new FormData();
formData.append('video', videoFile);

const response = await fetch('/api/v1/upload', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer YOUR_API_KEY'
  },
  body: formData
});

const result = await response.json();
const uploadId = result.id;

// 2. Verificar status periodicamente
const checkStatus = async () => {
  const statusResponse = await fetch(`/api/v1/status/${uploadId}`, {
    headers: { 'Authorization': 'Bearer YOUR_API_KEY' }
  });
  const status = await statusResponse.json();

  if (status.status === 'completed') {
    console.log('Processamento concluído!');
    console.log('Playlist URL:', status.playlist_url);
  } else if (status.status === 'failed') {
    console.log('Processamento falhou');
  } else {
    // Verificar novamente em 5 segundos
    setTimeout(checkStatus, 5000);
  }
};

checkStatus();
```

## Configuração

### Variáveis de Ambiente Principais

```bash
# Configuração básica
PORT=8090
ENVIRONMENT=development
VIDEO_DIR=./videos
MAX_FILE_SIZE=*********  # 100MB

# Sistema de filas
ENABLE_QUEUE=true
REDIS_HOST=localhost
REDIS_PORT=6379
WORKER_POOL_SIZE=3

# Autenticação
ENABLE_AUTH=false
API_KEYS=key1,key2,key3

# Storage
ENABLE_STORAGE=true
STORAGE_TYPE=r2  # ou local
R2_ENDPOINT=https://your-account.r2.cloudflarestorage.com
R2_ACCESS_KEY=your-access-key
R2_SECRET_KEY=your-secret-key
R2_BUCKET_NAME=your-bucket
```

### Escolhendo o Modo de Operação

**Use o Sistema com Fila quando:**
- Você tem múltiplos vídeos para processar
- Precisa de monitoramento em tempo real
- Quer retry automático de falhas
- Tem Redis disponível
- Precisa de estatísticas detalhadas

**Use o Sistema Tradicional quando:**
- Processamento simples e ocasional
- Não quer dependência do Redis
- Ambiente de desenvolvimento
- Recursos limitados

## Limites e Considerações

- Tamanho máximo de arquivo: 100MB (configurável via `MAX_FILE_SIZE`)
- Formatos suportados: MP4 (sistema tradicional), MP4/AVI/MOV/MKV (sistema com fila)
- Processamento paralelo: Até 3 qualidades simultâneas (configurável)
- Timeout de job: 5 minutos (configurável via `JOB_TIMEOUT`)
- Workers simultâneos: 3 (configurável via `WORKER_POOL_SIZE`)
- Retenção de arquivos: Configurável via limpeza automática
