# Integração Cloudflare R2

Este documento descreve como configurar e usar a integração com Cloudflare R2 no ApexStream.

## 📋 Visão Geral

O ApexStream suporta dois tipos de armazenamento:
- **Local**: Para desenvolvimento e testes (padrão)
- **Cloudflare R2**: Para produção com CDN global

Após o processamento do vídeo com FFmpeg, os arquivos HLS (`.m3u8` e `.ts`) são automaticamente enviados para o storage configurado.

## 🔧 Configuração do Cloudflare R2

### 1. Criar Bucket no R2

1. Acesse o painel do Cloudflare
2. Vá para **R2 Object Storage**
3. Clique em **Create bucket**
4. Escolha um nome único para o bucket
5. Selecione a região (recomendado: próxima aos usuários)

### 2. Configurar API Tokens

1. No painel R2, vá para **Manage R2 API tokens**
2. Clique em **Create API token**
3. Configure as permissões:
   - **Object Read**: Sim
   - **Object Write**: Sim
   - **Bucket List**: Sim (opcional)
4. Anote o **Access Key ID** e **Secret Access Key**

### 3. Obter Endpoint

O endpoint segue o formato:
```
https://<account-id>.r2.cloudflarestorage.com
```

Encontre seu Account ID no painel do Cloudflare (lado direito).

### 4. Configurar Domínio Personalizado (Opcional)

Para URLs públicas personalizadas:
1. No bucket, vá para **Settings**
2. Configure **Custom domain**
3. Adicione um registro CNAME no seu DNS

## ⚙️ Configuração da Aplicação

### Variáveis de Ambiente

Copie `.env.example` para `.env` e configure:

```bash
# Habilitar storage
ENABLE_STORAGE=true
STORAGE_TYPE=r2

# Configurações R2
R2_ENDPOINT=https://your-account-id.r2.cloudflarestorage.com
R2_ACCESS_KEY=your-access-key
R2_SECRET_KEY=your-secret-key
R2_BUCKET_NAME=your-bucket-name
R2_PUBLIC_URL=https://your-custom-domain.com  # Opcional
```

### Exemplo Completo

```bash
# Servidor
PORT=8080
ENVIRONMENT=production
VIDEO_DIR=./videos
MAX_FILE_SIZE=*********
ENABLE_FFMPEG=true

# Storage R2
ENABLE_STORAGE=true
STORAGE_TYPE=r2
R2_ENDPOINT=https://abc123def456.r2.cloudflarestorage.com
R2_ACCESS_KEY=1234567890abcdef1234567890abcdef
R2_SECRET_KEY=abcdef1234567890abcdef1234567890abcdef12
R2_BUCKET_NAME=apexstream-videos
R2_PUBLIC_URL=https://videos.meudominio.com
```

## 🚀 Como Funciona

### Fluxo de Upload e Processamento

1. **Upload**: Cliente envia vídeo MP4 via `POST /api/v1/upload`
2. **Processamento**: FFmpeg converte para HLS (`.m3u8` + `.ts`)
3. **Upload R2**: Arquivos são enviados para Cloudflare R2
4. **Resposta**: API retorna URL da playlist no R2

### Estrutura no R2

```
bucket-name/
└── videos/
    └── {upload-id}/
        ├── playlist.m3u8
        ├── segment_000.ts
        ├── segment_001.ts
        └── segment_002.ts
```

### URLs Geradas

- **Com domínio personalizado**: `https://videos.meudominio.com/videos/{id}/playlist.m3u8`
- **Sem domínio personalizado**: `https://bucket-name.r2.cloudflarestorage.com/videos/{id}/playlist.m3u8`

## 📊 Monitoramento

### Logs da Aplicação

```bash
# Inicialização
✅ Cliente R2 inicializado com sucesso - Bucket: apexstream-videos

# Upload
📊 Upload em lote concluído: 5 sucessos, 0 falhas de 5 arquivos totais
✅ Upload concluído - 5 arquivos enviados, URL da playlist: https://...
```

### Status via API

```bash
curl http://localhost:8080/api/v1/status/{upload-id}
```

Resposta:
```json
{
  "success": true,
  "id": "abc123",
  "status": "completed",
  "message": "Vídeo processado com sucesso",
  "playlist_url": "https://videos.meudominio.com/videos/abc123/playlist.m3u8",
  "duration": 120.5,
  "segment_count": 12,
  "uploaded_files": 13
}
```

## 🔄 Desenvolvimento vs Produção

### Desenvolvimento (Local)
```bash
STORAGE_TYPE=local
LOCAL_STORAGE_DIR=./storage
LOCAL_STORAGE_URL=/files
```

### Produção (R2)
```bash
STORAGE_TYPE=r2
R2_ENDPOINT=https://...
R2_ACCESS_KEY=...
R2_SECRET_KEY=...
R2_BUCKET_NAME=...
```

## 🛠️ Troubleshooting

### Erro: "configuração R2 incompleta"
- Verifique se todas as variáveis R2 estão definidas
- Confirme que o endpoint está correto

### Erro: "não foi possível acessar o bucket"
- Verifique as credenciais de acesso
- Confirme que o bucket existe
- Verifique as permissões do API token

### Erro: "falha na conexão com R2"
- Verifique conectividade com internet
- Confirme que o endpoint está acessível
- Verifique se não há firewall bloqueando

### URLs não funcionam
- Se usando domínio personalizado, verifique configuração DNS
- Confirme que o bucket permite acesso público (se necessário)
- Verifique se `R2_PUBLIC_URL` está configurado corretamente

## 💡 Dicas de Performance

1. **Região**: Escolha região R2 próxima aos usuários
2. **CDN**: Use Cloudflare CDN para melhor performance global
3. **Cache**: Configure headers de cache apropriados
4. **Compressão**: R2 suporta compressão automática

## 🔒 Segurança

1. **API Tokens**: Use tokens com permissões mínimas necessárias
2. **Rotação**: Rotacione tokens periodicamente
3. **Ambiente**: Nunca commite credenciais no código
4. **CORS**: Configure CORS no bucket se necessário para acesso web direto
