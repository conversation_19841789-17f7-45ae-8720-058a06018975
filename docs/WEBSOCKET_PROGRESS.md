# Sistema de WebSocket para Progresso de Encode

Este documento descreve como usar o sistema de WebSocket implementado para monitorar o progresso do encode de vídeo em tempo real.

## Visão Geral

O sistema permite que o frontend se conecte via WebSocket para receber atualizações em tempo real sobre o progresso do processamento de vídeo, incluindo:

- Progresso em porcentagem (0-100%)
- Status atual do job (processing, uploading, completed, failed)
- Mensagens descritivas
- Dad<PERSON> adicionais (duração, segmentos, etc.)

## Endpoints WebSocket

### Conexão Principal
```
GET /api/v1/ws/{jobId}
```

**Parâmetros:**
- `jobId`: ID do job de processamento de vídeo
- `user_id` (query, opcional): ID do usuário para identificação

**Exemplo de conexão:**
```javascript
const ws = new WebSocket('ws://localhost:8080/api/v1/ws/job_abc123?user_id=user1');
```

### Endpoints de Administração

#### Estatísticas WebSocket
```
GET /api/v1/ws/stats
```
Requer autenticação via API key.

#### Health Check
```
GET /api/v1/ws/health
```

#### Broadcast Manual
```
POST /api/v1/ws/broadcast
```
Requer autenticação via API key.

#### Envio Manual de Progresso
```
POST /api/v1/ws/{jobId}/progress
```
Requer autenticação via API key.

## Formato das Mensagens

### Mensagem de Progresso
```json
{
  "type": "progress",
  "job_id": "job_abc123",
  "progress": 45.5,
  "status": "processing",
  "message": "Processando: 23.2s de 51.0s",
  "timestamp": "2024-01-15T10:30:00Z",
  "data": {
    "current_time": 23.2,
    "total_duration": 51.0
  }
}
```

### Mensagem de Status
```json
{
  "type": "status",
  "job_id": "job_abc123",
  "status": "completed",
  "message": "Processamento concluído com sucesso",
  "timestamp": "2024-01-15T10:35:00Z",
  "data": {
    "duration": 51.0,
    "segments": 6,
    "playlist_path": "/path/to/playlist.m3u8"
  }
}
```

### Mensagem de Conexão
```json
{
  "type": "connected",
  "job_id": "job_abc123",
  "timestamp": "2024-01-15T10:25:00Z",
  "message": "Conectado ao sistema de progresso"
}
```

## Exemplo de Implementação Frontend

### JavaScript Vanilla
```javascript
class VideoProgressMonitor {
  constructor(jobId, userId = 'anonymous') {
    this.jobId = jobId;
    this.userId = userId;
    this.ws = null;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
  }

  connect() {
    const wsUrl = `ws://localhost:8080/api/v1/ws/${this.jobId}?user_id=${this.userId}`;
    this.ws = new WebSocket(wsUrl);

    this.ws.onopen = (event) => {
      console.log('WebSocket conectado:', event);
      this.reconnectAttempts = 0;
      this.onConnected();
    };

    this.ws.onmessage = (event) => {
      try {
        const message = JSON.parse(event.data);
        this.handleMessage(message);
      } catch (error) {
        console.error('Erro ao parsear mensagem WebSocket:', error);
      }
    };

    this.ws.onclose = (event) => {
      console.log('WebSocket desconectado:', event);
      this.onDisconnected();
      this.attemptReconnect();
    };

    this.ws.onerror = (error) => {
      console.error('Erro WebSocket:', error);
      this.onError(error);
    };
  }

  handleMessage(message) {
    switch (message.type) {
      case 'connected':
        this.onConnected();
        break;
      case 'progress':
        this.onProgress(message.progress, message.message, message.data);
        break;
      case 'status':
        this.onStatusChange(message.status, message.message, message.data);
        break;
      default:
        console.log('Mensagem desconhecida:', message);
    }
  }

  attemptReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      const delay = Math.pow(2, this.reconnectAttempts) * 1000; // Backoff exponencial
      console.log(`Tentando reconectar em ${delay}ms (tentativa ${this.reconnectAttempts})`);
      setTimeout(() => this.connect(), delay);
    }
  }

  // Callbacks que podem ser sobrescritos
  onConnected() {
    console.log('Conectado ao monitoramento de progresso');
  }

  onDisconnected() {
    console.log('Desconectado do monitoramento de progresso');
  }

  onProgress(progress, message, data) {
    console.log(`Progresso: ${progress}% - ${message}`, data);
    // Atualizar UI aqui
    this.updateProgressBar(progress);
    this.updateStatusMessage(message);
  }

  onStatusChange(status, message, data) {
    console.log(`Status: ${status} - ${message}`, data);
    // Atualizar UI baseado no status
    if (status === 'completed') {
      this.onCompleted(data);
    } else if (status === 'failed') {
      this.onFailed(message);
    }
  }

  onError(error) {
    console.error('Erro na conexão:', error);
  }

  onCompleted(data) {
    console.log('Processamento concluído:', data);
    this.updateProgressBar(100);
    this.updateStatusMessage('Concluído!');
  }

  onFailed(message) {
    console.error('Processamento falhou:', message);
    this.updateStatusMessage(`Erro: ${message}`);
  }

  updateProgressBar(progress) {
    const progressBar = document.getElementById('progress-bar');
    if (progressBar) {
      progressBar.style.width = `${progress}%`;
      progressBar.textContent = `${Math.round(progress)}%`;
    }
  }

  updateStatusMessage(message) {
    const statusElement = document.getElementById('status-message');
    if (statusElement) {
      statusElement.textContent = message;
    }
  }

  disconnect() {
    if (this.ws) {
      this.ws.close();
    }
  }
}

// Uso
const monitor = new VideoProgressMonitor('job_abc123', 'user1');
monitor.connect();
```

### HTML de Exemplo
```html
<!DOCTYPE html>
<html>
<head>
    <title>Monitor de Progresso de Vídeo</title>
    <style>
        .progress-container {
            width: 100%;
            background-color: #f0f0f0;
            border-radius: 5px;
            margin: 20px 0;
        }
        .progress-bar {
            width: 0%;
            height: 30px;
            background-color: #4CAF50;
            border-radius: 5px;
            text-align: center;
            line-height: 30px;
            color: white;
            transition: width 0.3s ease;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            background-color: #e7f3ff;
        }
    </style>
</head>
<body>
    <h1>Monitor de Progresso de Vídeo</h1>
    
    <div class="progress-container">
        <div id="progress-bar" class="progress-bar">0%</div>
    </div>
    
    <div id="status-message" class="status">Aguardando conexão...</div>
    
    <script>
        // Incluir o código JavaScript aqui
    </script>
</body>
</html>
```

## Estados do Job

- `pending`: Job na fila aguardando processamento
- `processing`: Vídeo sendo processado pelo FFmpeg
- `uploading`: Arquivos sendo enviados para storage
- `completed`: Processamento concluído com sucesso
- `failed`: Processamento falhou
- `retrying`: Tentando reprocessar após falha

## Considerações de Segurança

1. **Autenticação**: Considere implementar autenticação para conexões WebSocket em produção
2. **Rate Limiting**: Implemente limitação de taxa para evitar spam
3. **Validação de Origin**: Configure adequadamente o `CheckOrigin` no upgrader
4. **HTTPS/WSS**: Use conexões seguras em produção

## Troubleshooting

### Conexão Falha
- Verifique se o servidor está rodando
- Confirme se o job ID existe
- Verifique logs do servidor para erros

### Mensagens Não Chegam
- Verifique se o job está sendo processado
- Confirme se o WebSocket Hub está inicializado
- Verifique logs do worker pool

### Reconexão Automática
O exemplo inclui lógica de reconexão automática com backoff exponencial para lidar com desconexões temporárias.
