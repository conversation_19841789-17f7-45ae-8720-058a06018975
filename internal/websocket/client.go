package websocket

import (
	"encoding/json"
	"log"
	"net/http"
	"time"

	"github.com/gorilla/websocket"
)

// readPump bombeia mensagens da conexão WebSocket para o hub
func (c *Client) readPump() {
	defer func() {
		c.hub.unregister <- c
		c.conn.Close()
	}()

	c.conn.SetReadLimit(c.hub.config.MaxMessageSize)
	c.conn.SetReadDeadline(time.Now().Add(c.hub.config.PongWait))
	c.conn.SetPongHandler(func(string) error {
		c.conn.SetReadDeadline(time.Now().Add(c.hub.config.PongWait))
		return nil
	})

	for {
		_, message, err := c.conn.ReadMessage()
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				log.Printf("Erro na conexão WebSocket: %v", err)
			}
			break
		}

		// Processar mensagem recebida do cliente (se necessário)
		c.handleClientMessage(message)
	}
}

// writePump bombeia mensagens do hub para a conexão WebSocket
func (c *Client) writePump() {
	ticker := time.NewTicker(c.hub.config.PingPeriod)
	defer func() {
		ticker.Stop()
		c.conn.Close()
	}()

	for {
		select {
		case message, ok := <-c.send:
			c.conn.SetWriteDeadline(time.Now().Add(c.hub.config.WriteWait))
			if !ok {
				// O hub fechou o canal
				c.conn.WriteMessage(websocket.CloseMessage, []byte{})
				return
			}

			w, err := c.conn.NextWriter(websocket.TextMessage)
			if err != nil {
				return
			}
			w.Write(message)

			// Adicionar mensagens enfileiradas ao writer atual
			n := len(c.send)
			for i := 0; i < n; i++ {
				w.Write([]byte{'\n'})
				w.Write(<-c.send)
			}

			if err := w.Close(); err != nil {
				return
			}

		case <-ticker.C:
			c.conn.SetWriteDeadline(time.Now().Add(c.hub.config.WriteWait))
			if err := c.conn.WriteMessage(websocket.PingMessage, nil); err != nil {
				return
			}
		}
	}
}

// handleClientMessage processa mensagens recebidas do cliente
func (c *Client) handleClientMessage(message []byte) {
	var msg map[string]interface{}
	if err := json.Unmarshal(message, &msg); err != nil {
		log.Printf("Erro ao decodificar mensagem do cliente: %v", err)
		return
	}

	// Processar diferentes tipos de mensagens do cliente
	msgType, ok := msg["type"].(string)
	if !ok {
		return
	}

	switch msgType {
	case "ping":
		// Responder com pong
		c.sendPong()
	case "subscribe":
		// Permitir que o cliente se inscreva em um job específico
		if jobID, ok := msg["job_id"].(string); ok {
			c.subscribeToJob(jobID)
		}
	case "unsubscribe":
		// Permitir que o cliente se desinscreva de um job
		c.unsubscribeFromJob()
	}
}

// sendPong envia uma mensagem pong para o cliente
func (c *Client) sendPong() {
	pongMsg := map[string]interface{}{
		"type":      "pong",
		"timestamp": time.Now(),
	}

	if data, err := json.Marshal(pongMsg); err == nil {
		select {
		case c.send <- data:
		default:
			// Canal cheio, ignorar
		}
	}
}

// subscribeToJob inscreve o cliente em um job específico
func (c *Client) subscribeToJob(jobID string) {
	c.hub.mu.Lock()
	defer c.hub.mu.Unlock()

	// Remover da inscrição anterior se existir
	if c.jobID != "" {
		if jobClients, exists := c.hub.jobClients[c.jobID]; exists {
			delete(jobClients, c)
			if len(jobClients) == 0 {
				delete(c.hub.jobClients, c.jobID)
			}
		}
	}

	// Adicionar à nova inscrição
	c.jobID = jobID
	if c.hub.jobClients[jobID] == nil {
		c.hub.jobClients[jobID] = make(map[*Client]bool)
	}
	c.hub.jobClients[jobID][c] = true

	// Enviar confirmação
	confirmMsg := map[string]interface{}{
		"type":      "subscribed",
		"job_id":    jobID,
		"timestamp": time.Now(),
	}

	if data, err := json.Marshal(confirmMsg); err == nil {
		select {
		case c.send <- data:
		default:
			// Canal cheio, ignorar
		}
	}

	log.Printf("Cliente inscrito no job %s", jobID)
}

// unsubscribeFromJob remove a inscrição do cliente do job atual
func (c *Client) unsubscribeFromJob() {
	c.hub.mu.Lock()
	defer c.hub.mu.Unlock()

	if c.jobID != "" {
		if jobClients, exists := c.hub.jobClients[c.jobID]; exists {
			delete(jobClients, c)
			if len(jobClients) == 0 {
				delete(c.hub.jobClients, c.jobID)
			}
		}

		// Enviar confirmação
		confirmMsg := map[string]interface{}{
			"type":      "unsubscribed",
			"job_id":    c.jobID,
			"timestamp": time.Now(),
		}

		if data, err := json.Marshal(confirmMsg); err == nil {
			select {
			case c.send <- data:
			default:
				// Canal cheio, ignorar
			}
		}

		log.Printf("Cliente desinscrito do job %s", c.jobID)
		c.jobID = ""
	}
}

// ServeWS lida com requisições WebSocket do cliente
func ServeWS(hub *Hub, w http.ResponseWriter, r *http.Request, jobID, userID string) {
	conn, err := upgrader.Upgrade(w, r, nil)
	if err != nil {
		log.Printf("Erro ao fazer upgrade da conexão WebSocket: %v", err)
		return
	}

	client := &Client{
		hub:    hub,
		conn:   conn,
		send:   make(chan []byte, 256),
		jobID:  jobID,
		userID: userID,
	}

	client.hub.register <- client

	// Enviar mensagem de boas-vindas
	welcomeMsg := map[string]interface{}{
		"type":      "connected",
		"job_id":    jobID,
		"timestamp": time.Now(),
		"message":   "Conectado ao sistema de progresso",
	}

	if data, err := json.Marshal(welcomeMsg); err == nil {
		select {
		case client.send <- data:
		default:
			// Canal cheio, fechar conexão
			close(client.send)
			return
		}
	}

	// Permitir coleta de memória de referências ao hub e conn
	// executando todas as operações em novas goroutines
	go client.writePump()
	go client.readPump()
}

// SendMessage envia uma mensagem diretamente para este cliente
func (c *Client) SendMessage(messageType, message string, data map[string]interface{}) {
	msg := map[string]interface{}{
		"type":      messageType,
		"message":   message,
		"timestamp": time.Now(),
	}

	if data != nil {
		msg["data"] = data
	}

	if msgBytes, err := json.Marshal(msg); err == nil {
		select {
		case c.send <- msgBytes:
		default:
			// Canal cheio, ignorar mensagem
			log.Printf("Canal de envio cheio para cliente, mensagem descartada")
		}
	}
}

// IsConnected verifica se o cliente ainda está conectado
func (c *Client) IsConnected() bool {
	select {
	case <-c.send:
		return false
	default:
		return true
	}
}
