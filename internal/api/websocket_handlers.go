package api

import (
	"log"
	"net/http"

	"apexstream/internal/websocket"

	"github.com/gin-gonic/gin"
)

// WebSocketConfig contém configurações para WebSocket
type WebSocketConfig struct {
	Hub *websocket.Hub
}

// WebSocketProgressHandler lida com conexões WebSocket para monitoramento de progresso
func WebSocketProgressHandler(config *WebSocketConfig) gin.HandlerFunc {
	return func(c *gin.Context) {
		jobID := c.Param("jobId")
		if jobID == "" {
			c.JSON(http.StatusBadRequest, gin.H{
				"error": "job_id é obrigatório",
			})
			return
		}

		// Obter informações opcionais do cliente
		userID := c.Query("user_id")
		if userID == "" {
			userID = "anonymous"
		}

		log.Printf("Nova conexão WebSocket para job %s (user: %s)", jobID, userID)

		// Fazer upgrade da conexão HTTP para WebSocket
		websocket.ServeWS(config.Hub, c.<PERSON>, c.Request, jobID, userID)
	}
}

// WebSocketStatsHandler retorna estatísticas das conexões WebSocket
func WebSocketStatsHandler(config *WebSocketConfig) gin.HandlerFunc {
	return func(c *gin.Context) {
		stats := config.Hub.GetStats()
		c.JSON(http.StatusOK, gin.H{
			"status": "success",
			"data":   stats,
		})
	}
}

// WebSocketHealthHandler verifica a saúde do sistema WebSocket
func WebSocketHealthHandler(config *WebSocketConfig) gin.HandlerFunc {
	return func(c *gin.Context) {
		stats := config.Hub.GetStats()
		
		health := gin.H{
			"status":        "healthy",
			"websocket":     "active",
			"total_clients": stats["total_clients"],
			"jobs_monitored": stats["jobs_monitored"],
		}

		c.JSON(http.StatusOK, health)
	}
}

// BroadcastMessageHandler permite enviar mensagens para todos os clientes (admin only)
func BroadcastMessageHandler(config *WebSocketConfig) gin.HandlerFunc {
	return func(c *gin.Context) {
		var request struct {
			Type    string                 `json:"type" binding:"required"`
			Message string                 `json:"message" binding:"required"`
			Data    map[string]interface{} `json:"data"`
		}

		if err := c.ShouldBindJSON(&request); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"error": "Dados inválidos",
				"details": err.Error(),
			})
			return
		}

		// Enviar mensagem para todos os clientes conectados
		// Nota: Este é um endpoint administrativo, deve ter autenticação adequada
		config.Hub.SendJobStatus("broadcast", request.Type, request.Message, request.Data)

		c.JSON(http.StatusOK, gin.H{
			"status":  "success",
			"message": "Mensagem enviada para todos os clientes",
		})
	}
}

// SendJobProgressHandler permite enviar progresso manualmente para um job específico
func SendJobProgressHandler(config *WebSocketConfig) gin.HandlerFunc {
	return func(c *gin.Context) {
		jobID := c.Param("jobId")
		if jobID == "" {
			c.JSON(http.StatusBadRequest, gin.H{
				"error": "job_id é obrigatório",
			})
			return
		}

		var request struct {
			Progress float64               `json:"progress" binding:"required,min=0,max=100"`
			Status   string                `json:"status" binding:"required"`
			Message  string                `json:"message" binding:"required"`
			Data     map[string]interface{} `json:"data"`
		}

		if err := c.ShouldBindJSON(&request); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"error": "Dados inválidos",
				"details": err.Error(),
			})
			return
		}

		// Enviar atualização de progresso
		config.Hub.SendProgressUpdate(jobID, request.Progress, request.Status, request.Message, request.Data)

		c.JSON(http.StatusOK, gin.H{
			"status":  "success",
			"message": "Progresso enviado com sucesso",
			"job_id":  jobID,
		})
	}
}
