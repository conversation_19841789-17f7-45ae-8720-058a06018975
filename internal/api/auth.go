package api

import (
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
)

// AuthConfig contém as configurações de autenticação
type AuthConfig struct {
	APIKeys    []string // Lista de API keys válidas
	EnableAuth bool     // Habilitar/desabilitar autenticação
}

// AuthResponse representa uma resposta de erro de autenticação
type AuthResponse struct {
	Success bool   `json:"success"`
	Error   string `json:"error"`
	Message string `json:"message"`
}

// APIKeyAuthMiddleware middleware para autenticação com API key
func APIKeyAuthMiddleware(config *AuthConfig) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Se autenticação estiver desabilitada, prosseguir
		if !config.EnableAuth {
			c.Next()
			return
		}

		// Se não há API keys configuradas, bloquear tudo
		if len(config.APIKeys) == 0 {
			c.JSON(http.StatusInternalServerError, AuthResponse{
				Success: false,
				Error:   "auth_not_configured",
				Message: "Autenticação não está configurada corretamente no servidor.",
			})
			c.Abort()
			return
		}

		// Extrair API key do header Authorization
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.JSON(http.StatusUnauthorized, AuthResponse{
				Success: false,
				Error:   "missing_api_key",
				Message: "API key é obrigatória. Use o header 'Authorization: Bearer YOUR_API_KEY'.",
			})
			c.Abort()
			return
		}

		// Verificar formato do header (Bearer token)
		parts := strings.SplitN(authHeader, " ", 2)
		if len(parts) != 2 || strings.ToLower(parts[0]) != "bearer" {
			c.JSON(http.StatusUnauthorized, AuthResponse{
				Success: false,
				Error:   "invalid_auth_format",
				Message: "Formato de autenticação inválido. Use 'Authorization: Bearer YOUR_API_KEY'.",
			})
			c.Abort()
			return
		}

		apiKey := parts[1]

		// Verificar se a API key é válida
		if !isValidAPIKey(apiKey, config.APIKeys) {
			c.JSON(http.StatusUnauthorized, AuthResponse{
				Success: false,
				Error:   "invalid_api_key",
				Message: "API key inválida.",
			})
			c.Abort()
			return
		}

		// API key válida, prosseguir
		c.Set("api_key", apiKey) // Opcional: armazenar a API key no contexto
		c.Next()
	}
}

// isValidAPIKey verifica se uma API key é válida
func isValidAPIKey(apiKey string, validKeys []string) bool {
	for _, validKey := range validKeys {
		if apiKey == validKey {
			return true
		}
	}
	return false
}

// OptionalAPIKeyAuthMiddleware middleware opcional para autenticação
// Permite acesso sem API key, mas adiciona informações de autenticação se fornecida
func OptionalAPIKeyAuthMiddleware(config *AuthConfig) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Se autenticação estiver desabilitada, prosseguir
		if !config.EnableAuth {
			c.Next()
			return
		}

		// Extrair API key do header Authorization (opcional)
		authHeader := c.GetHeader("Authorization")
		if authHeader != "" {
			// Verificar formato do header (Bearer token)
			parts := strings.SplitN(authHeader, " ", 2)
			if len(parts) == 2 && strings.ToLower(parts[0]) == "bearer" {
				apiKey := parts[1]
				
				// Verificar se a API key é válida
				if isValidAPIKey(apiKey, config.APIKeys) {
					c.Set("authenticated", true)
					c.Set("api_key", apiKey)
				} else {
					c.Set("authenticated", false)
				}
			}
		} else {
			c.Set("authenticated", false)
		}

		c.Next()
	}
}

// GetAuthStatus retorna o status de autenticação do contexto
func GetAuthStatus(c *gin.Context) (bool, string) {
	authenticated, exists := c.Get("authenticated")
	if !exists {
		return false, ""
	}

	isAuth, ok := authenticated.(bool)
	if !ok || !isAuth {
		return false, ""
	}

	apiKey, exists := c.Get("api_key")
	if !exists {
		return true, ""
	}

	key, ok := apiKey.(string)
	if !ok {
		return true, ""
	}

	return true, key
}
