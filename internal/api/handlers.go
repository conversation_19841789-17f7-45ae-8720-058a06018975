package api

import (
	"context"
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"apexstream/internal/processor"
	"apexstream/internal/storage"

	"github.com/gin-gonic/gin"
)

type Config struct {
	VideoDir        string
	MaxFileSize     int64
	ProcessedDir    string
	EnableFFmpeg    bool
	VideoProcessor  *processor.VideoProcessor
	StorageProvider storage.StorageProvider
	EnableStorage   bool
}

type UploadResponse struct {
	Success          bool   `json:"success"`
	ID               string `json:"id"`
	Message          string `json:"message"`
	Filename         string `json:"filename"`
	Size             int64  `json:"size"`
	Timestamp        int64  `json:"timestamp"`
	ProcessingStatus string `json:"processing_status"`
}

type ErrorResponse struct {
	Success bool   `json:"success"`
	Error   string `json:"error"`
	Message string `json:"message"`
}

type StatusResponse struct {
	Success      bool    `json:"success"`
	ID           string  `json:"id"`
	Status       string  `json:"status"`
	Message      string  `json:"message"`
	PlaylistURL  string  `json:"playlist_url,omitempty"`
	Duration     float64 `json:"duration,omitempty"`
	SegmentCount int     `json:"segment_count,omitempty"`
	ProcessedAt  int64   `json:"processed_at,omitempty"`
}

// ProcessingStatus representa o status de processamento de um vídeo
type ProcessingStatus struct {
	ID            string                      `json:"id"`
	Status        string                      `json:"status"` // "pending", "processing", "uploading", "completed", "failed"
	Message       string                      `json:"message"`
	StartedAt     int64                       `json:"started_at"`
	CompletedAt   int64                       `json:"completed_at,omitempty"`
	PlaylistPath  string                      `json:"playlist_path,omitempty"`
	PlaylistURL   string                      `json:"playlist_url,omitempty"`
	Duration      float64                     `json:"duration,omitempty"`
	SegmentCount  int                         `json:"segment_count,omitempty"`
	UploadedFiles int                         `json:"uploaded_files,omitempty"`
	Error         string                      `json:"error,omitempty"`
	Result        *processor.ProcessingResult `json:"-"` // Não serializar no JSON
	StorageResult *storage.BatchUploadResult  `json:"-"` // Não serializar no JSON
}

// ProcessingStatusManager gerencia o status de processamento dos vídeos
type ProcessingStatusManager struct {
	statuses map[string]*ProcessingStatus
	mutex    sync.RWMutex
}

// NewProcessingStatusManager cria um novo gerenciador de status
func NewProcessingStatusManager() *ProcessingStatusManager {
	return &ProcessingStatusManager{
		statuses: make(map[string]*ProcessingStatus),
	}
}

// SetStatus define o status de processamento para um ID
func (psm *ProcessingStatusManager) SetStatus(id string, status *ProcessingStatus) {
	psm.mutex.Lock()
	defer psm.mutex.Unlock()
	psm.statuses[id] = status
}

// GetStatus obtém o status de processamento para um ID
func (psm *ProcessingStatusManager) GetStatus(id string) (*ProcessingStatus, bool) {
	psm.mutex.RLock()
	defer psm.mutex.RUnlock()
	status, exists := psm.statuses[id]
	return status, exists
}

// UpdateStatus atualiza campos específicos do status
func (psm *ProcessingStatusManager) UpdateStatus(id string, updateFunc func(*ProcessingStatus)) {
	psm.mutex.Lock()
	defer psm.mutex.Unlock()
	if status, exists := psm.statuses[id]; exists {
		updateFunc(status)
	}
}

// Instância global do gerenciador de status
var statusManager = NewProcessingStatusManager()

func generateID() string {
	bytes := make([]byte, 16)
	if _, err := rand.Read(bytes); err != nil {
		return fmt.Sprintf("%d", time.Now().UnixNano())
	}
	return hex.EncodeToString(bytes)
}

func validateVideoFile(filename string, size int64, maxSize int64) error {
	ext := strings.ToLower(filepath.Ext(filename))
	if ext != ".mp4" {
		return fmt.Errorf("apenas arquivos .mp4 são aceitos, recebido: %s", ext)
	}

	if size > maxSize {
		return fmt.Errorf("arquivo muito grande: %d bytes, máximo permitido: %d bytes", size, maxSize)
	}

	if size == 0 {
		return fmt.Errorf("arquivo vazio")
	}

	return nil
}

// processVideoAsync processa um vídeo de forma assíncrona
func processVideoAsync(config *Config, inputPath, uploadID string) {
	log.Printf("Iniciando processamento assíncrono para ID: %s", uploadID)

	// Atualizar status para "processing"
	statusManager.UpdateStatus(uploadID, func(status *ProcessingStatus) {
		status.Status = "processing"
		status.Message = "Processando vídeo com FFmpeg..."
	})

	// Processar vídeo
	result, err := config.VideoProcessor.ProcessToHLS(inputPath, uploadID)

	if err != nil {
		log.Printf("Erro no processamento do vídeo %s: %v", uploadID, err)

		// Atualizar status para "failed"
		statusManager.UpdateStatus(uploadID, func(status *ProcessingStatus) {
			status.Status = "failed"
			status.Message = "Falha no processamento do vídeo"
			status.Error = err.Error()
			status.CompletedAt = time.Now().Unix()
		})
		return
	}

	if !result.Success {
		log.Printf("Processamento falhou para vídeo %s: %s", uploadID, result.Error)

		// Atualizar status para "failed"
		statusManager.UpdateStatus(uploadID, func(status *ProcessingStatus) {
			status.Status = "failed"
			status.Message = "Processamento do vídeo falhou"
			status.Error = result.Error
			status.CompletedAt = time.Now().Unix()
		})
		return
	}

	log.Printf("Processamento concluído com sucesso para ID: %s", uploadID)

	// Upload para storage se habilitado
	var playlistURL string
	if config.EnableStorage && config.StorageProvider != nil {
		playlistURL = uploadToStorage(config.StorageProvider, result, uploadID)
	}

	// Atualizar status para "completed"
	statusManager.UpdateStatus(uploadID, func(status *ProcessingStatus) {
		status.Status = "completed"
		status.Message = "Vídeo processado com sucesso"
		status.PlaylistPath = result.PlaylistPath
		status.PlaylistURL = playlistURL
		status.Duration = result.Duration
		status.SegmentCount = len(result.SegmentPaths)
		status.CompletedAt = time.Now().Unix()
		status.Result = result
	})

	// Limpar arquivo original (opcional)
	if err := os.Remove(inputPath); err != nil {
		log.Printf("Aviso: não foi possível remover arquivo original %s: %v", inputPath, err)
	} else {
		log.Printf("Arquivo original removido: %s", inputPath)
	}
}

// uploadToStorage faz upload dos arquivos processados para o storage
func uploadToStorage(storageProvider storage.StorageProvider, result *processor.ProcessingResult, uploadID string) string {
	log.Printf("Iniciando upload para storage - ID: %s", uploadID)

	// Atualizar status para "uploading"
	statusManager.UpdateStatus(uploadID, func(status *ProcessingStatus) {
		status.Status = "uploading"
		status.Message = "Fazendo upload dos arquivos processados..."
	})

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Minute)
	defer cancel()

	// Determinar diretório dos arquivos processados
	outputDir := filepath.Dir(result.PlaylistPath)
	keyPrefix := fmt.Sprintf("videos/%s", uploadID)

	// Fazer upload do diretório completo
	batchResult, err := storageProvider.UploadDirectory(ctx, outputDir, keyPrefix)
	if err != nil {
		log.Printf("Erro no upload para storage: %v", err)
		statusManager.UpdateStatus(uploadID, func(status *ProcessingStatus) {
			status.Status = "failed"
			status.Message = "Falha no upload para storage"
			status.Error = err.Error()
			status.CompletedAt = time.Now().Unix()
		})
		return ""
	}

	// Atualizar status com informações do upload
	statusManager.UpdateStatus(uploadID, func(status *ProcessingStatus) {
		status.UploadedFiles = batchResult.SuccessCount
		status.StorageResult = batchResult
	})

	// Encontrar URL da playlist
	playlistKey := fmt.Sprintf("%s/playlist.m3u8", keyPrefix)
	playlistURL := storageProvider.GetPublicURL(playlistKey)

	log.Printf("Upload concluído - %d arquivos enviados, URL da playlist: %s",
		batchResult.SuccessCount, playlistURL)

	return playlistURL
}

// UploadHandler manipula o upload de vídeos
func UploadHandler(config *Config) gin.HandlerFunc {
	return func(c *gin.Context) {
		log.Printf("Iniciando upload de vídeo...")

		file, header, err := c.Request.FormFile("video")
		if err != nil {
			log.Printf("Erro ao obter arquivo do formulário: %v", err)
			c.JSON(http.StatusBadRequest, ErrorResponse{
				Success: false,
				Error:   "missing_file",
				Message: "Nenhum arquivo foi enviado. Use o campo 'video' no formulário.",
			})
			return
		}
		defer file.Close()

		if err := validateVideoFile(header.Filename, header.Size, config.MaxFileSize); err != nil {
			log.Printf("Validação de arquivo falhou: %v", err)
			c.JSON(http.StatusBadRequest, ErrorResponse{
				Success: false,
				Error:   "invalid_file",
				Message: err.Error(),
			})
			return
		}

		uploadID := generateID()
		log.Printf("ID do upload gerado: %s", uploadID)

		fileExt := filepath.Ext(header.Filename)
		filename := fmt.Sprintf("%s%s", uploadID, fileExt)
		filePath := filepath.Join(config.VideoDir, filename)

		dst, err := os.Create(filePath)
		if err != nil {
			log.Printf("Erro ao criar arquivo de destino: %v", err)
			c.JSON(http.StatusInternalServerError, ErrorResponse{
				Success: false,
				Error:   "file_creation_error",
				Message: "Erro interno ao salvar o arquivo.",
			})
			return
		}
		defer dst.Close()

		bytesWritten, err := io.Copy(dst, file)
		if err != nil {
			log.Printf("Erro ao copiar arquivo: %v", err)
			os.Remove(filePath)
			c.JSON(http.StatusInternalServerError, ErrorResponse{
				Success: false,
				Error:   "file_copy_error",
				Message: "Erro ao salvar o arquivo.",
			})
			return
		}

		log.Printf("Upload concluído com sucesso: %s (%d bytes)", filename, bytesWritten)

		// Inicializar status de processamento
		processingStatus := &ProcessingStatus{
			ID:        uploadID,
			Status:    "pending",
			Message:   "Vídeo enviado, aguardando processamento",
			StartedAt: time.Now().Unix(),
		}
		statusManager.SetStatus(uploadID, processingStatus)

		// Iniciar processamento assíncrono se FFmpeg estiver habilitado
		if config.EnableFFmpeg && config.VideoProcessor != nil {
			go processVideoAsync(config, filePath, uploadID)
		}

		c.JSON(http.StatusOK, UploadResponse{
			Success:          true,
			ID:               uploadID,
			Message:          "Upload realizado com sucesso. O processamento será iniciado em breve.",
			Filename:         filename,
			Size:             bytesWritten,
			Timestamp:        time.Now().Unix(),
			ProcessingStatus: processingStatus.Status,
		})
	}
}

// StatusHandler verifica o status de processamento de um vídeo
func StatusHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		id := c.Param("id")

		if id == "" {
			c.JSON(http.StatusBadRequest, ErrorResponse{
				Success: false,
				Error:   "missing_id",
				Message: "ID é obrigatório.",
			})
			return
		}

		log.Printf("Verificando status para ID: %s", id)

		// Buscar status no gerenciador
		status, exists := statusManager.GetStatus(id)
		if !exists {
			c.JSON(http.StatusNotFound, ErrorResponse{
				Success: false,
				Error:   "video_not_found",
				Message: "Vídeo não encontrado ou ID inválido.",
			})
			return
		}

		// Construir resposta baseada no status atual
		response := StatusResponse{
			Success:      true,
			ID:           status.ID,
			Status:       status.Status,
			Message:      status.Message,
			Duration:     status.Duration,
			SegmentCount: status.SegmentCount,
			ProcessedAt:  status.CompletedAt,
		}

		// Se processamento foi concluído, incluir URL da playlist
		if status.Status == "completed" {
			if status.PlaylistURL != "" {
				// Usar URL do storage se disponível
				response.PlaylistURL = status.PlaylistURL
			} else if status.PlaylistPath != "" {
				// Fallback para URL local
				response.PlaylistURL = fmt.Sprintf("/api/v1/videos/%s/playlist.m3u8", id)
			}
		}

		c.JSON(http.StatusOK, response)
	}
}

// NewVideoProcessorConfig cria uma configuração para o processador de vídeo
func NewVideoProcessorConfig(processedDir string) *processor.ProcessorConfig {
	return &processor.ProcessorConfig{
		FFmpegPath:    "ffmpeg",                            // Assume que está no PATH
		OutputDir:     processedDir,                        // Diretório configurado
		TempDir:       filepath.Join(processedDir, "temp"), // Subdiretório temp
		SegmentTime:   10,                                  // 10 segundos por segmento
		PlaylistType:  "vod",                               // Video on Demand
		VideoCodec:    "libx264",                           // H.264
		AudioCodec:    "aac",                               // AAC
		VideoBitrates: []int{720, 480, 360},                // Múltiplas resoluções
	}
}

// InitializeVideoProcessor inicializa o processador de vídeo na configuração
func InitializeVideoProcessor(config *Config) error {
	if !config.EnableFFmpeg {
		log.Printf("FFmpeg desabilitado na configuração")
		return nil
	}

	if config.ProcessedDir == "" {
		config.ProcessedDir = filepath.Join(config.VideoDir, "processed")
	}

	// Criar diretórios necessários
	if err := os.MkdirAll(config.ProcessedDir, 0755); err != nil {
		return fmt.Errorf("erro ao criar diretório de processamento: %v", err)
	}

	// Criar configuração do processador
	processorConfig := NewVideoProcessorConfig(config.ProcessedDir)

	// Inicializar processador
	config.VideoProcessor = processor.NewVideoProcessor(processorConfig)

	// Validar FFmpeg
	if err := config.VideoProcessor.ValidateFFmpeg(); err != nil {
		return fmt.Errorf("validação do FFmpeg falhou: %v", err)
	}

	log.Printf("✅ Processador de vídeo inicializado com sucesso")
	log.Printf("📁 Diretório de saída: %s", config.ProcessedDir)

	return nil
}
