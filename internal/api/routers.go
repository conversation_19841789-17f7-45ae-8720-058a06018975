package api

import (
	"apexstream/internal/storage"
	"log"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
)

type RouterConfig struct {
	VideoDir        string
	MaxFileSize     int64
	Environment     string
	ProcessedDir    string
	EnableFFmpeg    bool
	StorageProvider storage.StorageProvider
	EnableStorage   bool
	APIKeys         []string // Lista de API keys válidas
	EnableAuth      bool     // Habilitar/desabilitar autenticação
}

func SetupRouter(config *RouterConfig) *gin.Engine {
	if config.Environment == "production" {
		gin.SetMode(gin.ReleaseMode)
	}

	router := gin.New()

	setupMiddlewares(router)

	setupSystemRoutes(router)

	setupAPIRoutes(router, config)

	return router
}

func setupMiddlewares(router *gin.Engine) {
	router.Use(gin.Logger())

	router.Use(gin.Recovery())

	router.Use(corsMiddleware())
}

func corsMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.<PERSON>("Access-Control-Allow-Origin", "*")
		c.<PERSON>("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Content-Type, Authorization")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}

		c.Next()
	}
}

func setupSystemRoutes(router *gin.Engine) {
	router.GET("/health", healthCheckHandler)

	router.GET("/", apiInfoHandler)
}

func setupAPIRoutes(router *gin.Engine, config *RouterConfig) {
	apiConfig := &Config{
		VideoDir:        config.VideoDir,
		MaxFileSize:     config.MaxFileSize,
		ProcessedDir:    config.ProcessedDir,
		EnableFFmpeg:    config.EnableFFmpeg,
		StorageProvider: config.StorageProvider,
		EnableStorage:   config.EnableStorage,
	}

	// Configuração de autenticação
	authConfig := &AuthConfig{
		APIKeys:    config.APIKeys,
		EnableAuth: config.EnableAuth,
	}

	// Inicializar processador de vídeo se habilitado
	if err := InitializeVideoProcessor(apiConfig); err != nil {
		log.Printf("⚠️  Aviso: Falha ao inicializar processador de vídeo: %v", err)
		log.Printf("📝 O sistema funcionará apenas com upload, sem processamento FFmpeg")
		apiConfig.EnableFFmpeg = false
	}

	v1 := router.Group("/api/v1")
	{
		// Rotas protegidas por autenticação
		setupVideoRoutes(v1, apiConfig, authConfig)

		// Rotas de status (opcional: podem ser protegidas ou não)
		setupStatusRoutes(v1, authConfig)

		// Rotas futuras podem ser adicionadas aqui
		// setupAuthRoutes(v1, authConfig)
		// setupProcessingRoutes(v1, processingConfig)
	}
}

func setupVideoRoutes(group *gin.RouterGroup, config *Config, authConfig *AuthConfig) {
	// POST /api/v1/upload - Upload de vídeo (protegido por autenticação)
	group.POST("/upload", APIKeyAuthMiddleware(authConfig), UploadHandler(config))

	// Rotas futuras para vídeos:
	// GET /api/v1/videos - Listar vídeos
	// GET /api/v1/videos/:id - Obter detalhes de um vídeo
	// DELETE /api/v1/videos/:id - Deletar um vídeo
}

func setupStatusRoutes(group *gin.RouterGroup, authConfig *AuthConfig) {
	// GET /api/v1/status/:id - Verificar status de processamento (autenticação opcional)
	group.GET("/status/:id", OptionalAPIKeyAuthMiddleware(authConfig), StatusHandler())

	// Rotas futuras para status:
	// GET /api/v1/status - Listar todos os status
	// GET /api/v1/processing/queue - Ver fila de processamento
}

func healthCheckHandler(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"status":    "ok",
		"timestamp": time.Now().Unix(),
		"service":   "apexstream",
		"version":   "1.0.0",
	})
}

func apiInfoHandler(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"service":     "ApexStream Video Processing API",
		"version":     "1.0.0",
		"description": "API para upload e processamento de vídeos com conversão HLS",
		"endpoints": gin.H{
			"health":   "GET /health",
			"upload":   "POST /api/v1/upload (requer autenticação se habilitada)",
			"status":   "GET /api/v1/status/:id (autenticação opcional)",
			"api_info": "GET /",
		},
		"authentication": gin.H{
			"type":        "API Key",
			"header":      "Authorization: Bearer YOUR_API_KEY",
			"description": "Use o header Authorization com Bearer token para autenticação",
		},
		"documentation": "https://github.com/sondtheanime/apexstream",
		"timestamp":     time.Now().Unix(),
	})
}

// Funções auxiliares para configurações futuras

// setupAuthRoutes configurará as rotas de autenticação (futuro)
/*
func setupAuthRoutes(group *gin.RouterGroup, config *AuthConfig) {
	auth := group.Group("/auth")
	{
		auth.POST("/login", LoginHandler(config))
		auth.POST("/register", RegisterHandler(config))
		auth.POST("/refresh", RefreshTokenHandler(config))
		auth.POST("/logout", LogoutHandler(config))
	}
}
*/

// setupProcessingRoutes configurará as rotas de processamento avançado (futuro)
/*
func setupProcessingRoutes(group *gin.RouterGroup, config *ProcessingConfig) {
	processing := group.Group("/processing")
	{
		processing.GET("/queue", QueueStatusHandler(config))
		processing.POST("/retry/:id", RetryProcessingHandler(config))
		processing.DELETE("/cancel/:id", CancelProcessingHandler(config))
	}
}
*/

// setupAdminRoutes configurará as rotas administrativas (futuro)
/*
func setupAdminRoutes(group *gin.RouterGroup, config *AdminConfig) {
	admin := group.Group("/admin")
	admin.Use(AdminAuthMiddleware()) // Middleware de autenticação admin
	{
		admin.GET("/stats", StatsHandler(config))
		admin.GET("/logs", LogsHandler(config))
		admin.POST("/maintenance", MaintenanceHandler(config))
	}
}
*/
