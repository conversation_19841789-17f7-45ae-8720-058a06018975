package storage

import (
	"context"
	"fmt"
	"io"
	"log"
	"os"
	"path/filepath"
	"strings"
	"time"
)

// LocalConfig contém as configurações para armazenamento local
type LocalConfig struct {
	BaseDir   string // Diretório base para armazenamento
	BaseURL   string // URL base para servir arquivos (ex: http://localhost:8080/files)
	CreateDir bool   // Criar diretório se não existir
}

// LocalClient implementa armazenamento local para desenvolvimento/testes
type LocalClient struct {
	config *LocalConfig
}

// NewLocalClient cria um novo cliente de armazenamento local
func NewLocalClient(cfg *LocalConfig) (*LocalClient, error) {
	if cfg.BaseDir == "" {
		return nil, fmt.Errorf("BaseDir é obrigatório para armazenamento local")
	}

	// Criar diretório se necessário
	if cfg.CreateDir {
		if err := os.Mkdir<PERSON>ll(cfg.BaseDir, 0755); err != nil {
			return nil, fmt.Errorf("erro ao criar diretório base %s: %v", cfg.BaseDir, err)
		}
	}

	// Verificar se diretório existe
	if _, err := os.Stat(cfg.BaseDir); os.IsNotExist(err) {
		return nil, fmt.Errorf("diretório base não existe: %s", cfg.BaseDir)
	}

	// URL base padrão se não especificada
	if cfg.BaseURL == "" {
		cfg.BaseURL = "/files"
	}

	client := &LocalClient{
		config: cfg,
	}

	log.Printf("✅ Cliente de armazenamento local inicializado - BaseDir: %s", cfg.BaseDir)
	return client, nil
}

// UploadFile copia um arquivo para o armazenamento local
func (l *LocalClient) UploadFile(ctx context.Context, filePath, key string) (*UploadResult, error) {
	// Abrir arquivo origem
	srcFile, err := os.Open(filePath)
	if err != nil {
		return nil, fmt.Errorf("erro ao abrir arquivo origem %s: %v", filePath, err)
	}
	defer srcFile.Close()

	// Obter informações do arquivo
	fileInfo, err := srcFile.Stat()
	if err != nil {
		return nil, fmt.Errorf("erro ao obter informações do arquivo: %v", err)
	}

	// Construir caminho de destino
	destPath := filepath.Join(l.config.BaseDir, key)
	
	// Criar diretórios necessários
	destDir := filepath.Dir(destPath)
	if err := os.MkdirAll(destDir, 0755); err != nil {
		return nil, fmt.Errorf("erro ao criar diretório de destino %s: %v", destDir, err)
	}

	// Criar arquivo de destino
	destFile, err := os.Create(destPath)
	if err != nil {
		return nil, fmt.Errorf("erro ao criar arquivo de destino %s: %v", destPath, err)
	}
	defer destFile.Close()

	// Copiar conteúdo
	bytesWritten, err := io.Copy(destFile, srcFile)
	if err != nil {
		return nil, fmt.Errorf("erro ao copiar arquivo: %v", err)
	}

	// Construir URL pública
	publicURL := l.buildPublicURL(key)

	result := &UploadResult{
		Key:       key,
		URL:       publicURL,
		Size:      bytesWritten,
		ETag:      fmt.Sprintf("%d-%d", fileInfo.ModTime().Unix(), fileInfo.Size()),
		Timestamp: time.Now().Unix(),
	}

	log.Printf("✅ Arquivo copiado localmente: %s -> %s (%d bytes)", filePath, destPath, bytesWritten)
	return result, nil
}

// UploadDirectory copia todos os arquivos de um diretório
func (l *LocalClient) UploadDirectory(ctx context.Context, localDir, keyPrefix string) (*BatchUploadResult, error) {
	result := &BatchUploadResult{
		Success: make([]UploadResult, 0),
		Failed:  make([]UploadError, 0),
	}

	err := filepath.Walk(localDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// Pular diretórios
		if info.IsDir() {
			return nil
		}

		result.TotalFiles++

		// Calcular chave relativa
		relPath, err := filepath.Rel(localDir, path)
		if err != nil {
			result.Failed = append(result.Failed, UploadError{
				FilePath: path,
				Key:      "",
				Error:    fmt.Sprintf("erro ao calcular caminho relativo: %v", err),
			})
			result.FailedCount++
			return nil
		}

		// Construir chave final
		key := filepath.Join(keyPrefix, relPath)
		key = strings.ReplaceAll(key, "\\", "/") // Normalizar separadores

		// Fazer "upload" (cópia local)
		uploadResult, err := l.UploadFile(ctx, path, key)
		if err != nil {
			result.Failed = append(result.Failed, UploadError{
				FilePath: path,
				Key:      key,
				Error:    err.Error(),
			})
			result.FailedCount++
			return nil
		}

		result.Success = append(result.Success, *uploadResult)
		result.SuccessCount++
		return nil
	})

	if err != nil {
		return nil, fmt.Errorf("erro ao percorrer diretório: %v", err)
	}

	log.Printf("📊 Cópia local em lote concluída: %d sucessos, %d falhas de %d arquivos totais",
		result.SuccessCount, result.FailedCount, result.TotalFiles)

	return result, nil
}

// DeleteFile remove um arquivo do armazenamento local
func (l *LocalClient) DeleteFile(ctx context.Context, key string) error {
	filePath := filepath.Join(l.config.BaseDir, key)
	
	if err := os.Remove(filePath); err != nil {
		if os.IsNotExist(err) {
			return nil // Arquivo já não existe
		}
		return fmt.Errorf("erro ao deletar arquivo %s: %v", filePath, err)
	}

	log.Printf("🗑️  Arquivo local deletado: %s", filePath)
	return nil
}

// FileExists verifica se um arquivo existe no armazenamento local
func (l *LocalClient) FileExists(ctx context.Context, key string) (bool, error) {
	filePath := filepath.Join(l.config.BaseDir, key)
	
	_, err := os.Stat(filePath)
	if err != nil {
		if os.IsNotExist(err) {
			return false, nil
		}
		return false, fmt.Errorf("erro ao verificar existência do arquivo: %v", err)
	}
	
	return true, nil
}

// GetPublicURL retorna a URL pública de um arquivo
func (l *LocalClient) GetPublicURL(key string) string {
	return l.buildPublicURL(key)
}

// buildPublicURL constrói a URL pública para um arquivo
func (l *LocalClient) buildPublicURL(key string) string {
	baseURL := strings.TrimRight(l.config.BaseURL, "/")
	return fmt.Sprintf("%s/%s", baseURL, key)
}

// Verificar se LocalClient implementa StorageProvider
var _ StorageProvider = (*LocalClient)(nil)
