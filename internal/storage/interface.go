package storage

import "context"

// StorageProvider define a interface para provedores de armazenamento
type StorageProvider interface {
	// UploadFile faz upload de um único arquivo
	UploadFile(ctx context.Context, filePath, key string) (*UploadResult, error)
	
	// UploadDirectory faz upload de todos os arquivos de um diretório
	UploadDirectory(ctx context.Context, localDir, keyPrefix string) (*BatchUploadResult, error)
	
	// DeleteFile remove um arquivo
	DeleteFile(ctx context.Context, key string) error
	
	// FileExists verifica se um arquivo existe
	FileExists(ctx context.Context, key string) (bool, error)
	
	// GetPublicURL retorna a URL pública de um arquivo
	GetPublicURL(key string) string
}

// Verificar se R2Client implementa StorageProvider
var _ StorageProvider = (*R2Client)(nil)
