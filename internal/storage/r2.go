package storage

import (
	"context"
	"fmt"
	"log"
	"mime"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/credentials"
	"github.com/aws/aws-sdk-go-v2/service/s3"
)

// R2Config contém as configurações para Cloudflare R2
type R2Config struct {
	Endpoint   string
	AccessKey  string
	SecretKey  string
	BucketName string
	Region     string // Cloudflare R2 usa "auto" como região
	PublicURL  string // URL pública do bucket (opcional)
}

// R2Client é o cliente para interagir com Cloudflare R2
type R2Client struct {
	client     *s3.Client
	config     *R2Config
	bucketName string
}

// UploadResult contém informações sobre um arquivo enviado
type UploadResult struct {
	Key       string `json:"key"`
	URL       string `json:"url"`
	Size      int64  `json:"size"`
	ETag      string `json:"etag"`
	Timestamp int64  `json:"timestamp"`
}

// BatchUploadResult contém resultados de upload em lote
type BatchUploadResult struct {
	Success      []UploadResult `json:"success"`
	Failed       []UploadError  `json:"failed"`
	TotalFiles   int            `json:"total_files"`
	SuccessCount int            `json:"success_count"`
	FailedCount  int            `json:"failed_count"`
}

// UploadError representa um erro de upload
type UploadError struct {
	FilePath string `json:"file_path"`
	Key      string `json:"key"`
	Error    string `json:"error"`
}

// NewR2Client cria um novo cliente R2
func NewR2Client(cfg *R2Config) (*R2Client, error) {
	if cfg.Endpoint == "" || cfg.AccessKey == "" || cfg.SecretKey == "" || cfg.BucketName == "" {
		return nil, fmt.Errorf("configuração R2 incompleta: endpoint, access_key, secret_key e bucket_name são obrigatórios")
	}

	// Configurar região padrão se não especificada
	if cfg.Region == "" {
		cfg.Region = "auto"
	}

	// Configurar credenciais estáticas
	creds := credentials.NewStaticCredentialsProvider(cfg.AccessKey, cfg.SecretKey, "")

	// Configurar cliente AWS SDK
	awsConfig, err := config.LoadDefaultConfig(context.TODO(),
		config.WithCredentialsProvider(creds),
		config.WithRegion(cfg.Region),
	)
	if err != nil {
		return nil, fmt.Errorf("erro ao carregar configuração AWS: %v", err)
	}

	// Criar cliente S3 com endpoint personalizado para R2
	s3Client := s3.NewFromConfig(awsConfig, func(o *s3.Options) {
		o.BaseEndpoint = aws.String(cfg.Endpoint)
		o.UsePathStyle = true // R2 requer path-style URLs
	})

	client := &R2Client{
		client:     s3Client,
		config:     cfg,
		bucketName: cfg.BucketName,
	}

	// Verificar conectividade
	if err := client.testConnection(); err != nil {
		return nil, fmt.Errorf("falha na conexão com R2: %v", err)
	}

	log.Printf("✅ Cliente R2 inicializado com sucesso - Bucket: %s", cfg.BucketName)
	return client, nil
}

// testConnection verifica se a conexão com R2 está funcionando
func (r *R2Client) testConnection() error {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	_, err := r.client.HeadBucket(ctx, &s3.HeadBucketInput{
		Bucket: aws.String(r.bucketName),
	})
	if err != nil {
		return fmt.Errorf("não foi possível acessar o bucket %s: %v", r.bucketName, err)
	}

	return nil
}

// UploadFile faz upload de um único arquivo para R2
func (r *R2Client) UploadFile(ctx context.Context, filePath, key string) (*UploadResult, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return nil, fmt.Errorf("erro ao abrir arquivo %s: %v", filePath, err)
	}
	defer file.Close()

	// Obter informações do arquivo
	fileInfo, err := file.Stat()
	if err != nil {
		return nil, fmt.Errorf("erro ao obter informações do arquivo: %v", err)
	}

	// Determinar Content-Type baseado na extensão
	contentType := mime.TypeByExtension(filepath.Ext(filePath))
	if contentType == "" {
		contentType = "application/octet-stream"
	}

	// Configurações especiais para arquivos HLS
	if strings.HasSuffix(filePath, ".m3u8") {
		contentType = "application/vnd.apple.mpegurl"
	} else if strings.HasSuffix(filePath, ".ts") {
		contentType = "video/mp2t"
	}

	// Fazer upload
	putInput := &s3.PutObjectInput{
		Bucket:        aws.String(r.bucketName),
		Key:           aws.String(key),
		Body:          file,
		ContentType:   aws.String(contentType),
		ContentLength: aws.Int64(fileInfo.Size()),
		CacheControl:  aws.String("public, max-age=31536000"), // Cache por 1 ano
	}

	result, err := r.client.PutObject(ctx, putInput)
	if err != nil {
		return nil, fmt.Errorf("erro no upload para R2: %v", err)
	}

	// Construir URL pública
	publicURL := r.buildPublicURL(key)

	uploadResult := &UploadResult{
		Key:       key,
		URL:       publicURL,
		Size:      fileInfo.Size(),
		ETag:      strings.Trim(*result.ETag, "\""),
		Timestamp: time.Now().Unix(),
	}

	log.Printf("✅ Upload concluído: %s -> %s (%d bytes)", filePath, key, fileInfo.Size())
	return uploadResult, nil
}

// UploadDirectory faz upload de todos os arquivos de um diretório
func (r *R2Client) UploadDirectory(ctx context.Context, localDir, keyPrefix string) (*BatchUploadResult, error) {
	result := &BatchUploadResult{
		Success: make([]UploadResult, 0),
		Failed:  make([]UploadError, 0),
	}

	err := filepath.Walk(localDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// Pular diretórios
		if info.IsDir() {
			return nil
		}

		result.TotalFiles++

		// Calcular chave relativa
		relPath, err := filepath.Rel(localDir, path)
		if err != nil {
			result.Failed = append(result.Failed, UploadError{
				FilePath: path,
				Key:      "",
				Error:    fmt.Sprintf("erro ao calcular caminho relativo: %v", err),
			})
			result.FailedCount++
			return nil
		}

		// Construir chave final
		key := filepath.Join(keyPrefix, relPath)
		key = strings.ReplaceAll(key, "\\", "/") // Normalizar separadores para URLs

		// Fazer upload
		uploadResult, err := r.UploadFile(ctx, path, key)
		if err != nil {
			result.Failed = append(result.Failed, UploadError{
				FilePath: path,
				Key:      key,
				Error:    err.Error(),
			})
			result.FailedCount++
			return nil
		}

		result.Success = append(result.Success, *uploadResult)
		result.SuccessCount++
		return nil
	})

	if err != nil {
		return nil, fmt.Errorf("erro ao percorrer diretório: %v", err)
	}

	log.Printf("📊 Upload em lote concluído: %d sucessos, %d falhas de %d arquivos totais",
		result.SuccessCount, result.FailedCount, result.TotalFiles)

	return result, nil
}

// DeleteFile remove um arquivo do R2
func (r *R2Client) DeleteFile(ctx context.Context, key string) error {
	_, err := r.client.DeleteObject(ctx, &s3.DeleteObjectInput{
		Bucket: aws.String(r.bucketName),
		Key:    aws.String(key),
	})
	if err != nil {
		return fmt.Errorf("erro ao deletar arquivo %s: %v", key, err)
	}

	log.Printf("🗑️  Arquivo deletado: %s", key)
	return nil
}

// FileExists verifica se um arquivo existe no R2
func (r *R2Client) FileExists(ctx context.Context, key string) (bool, error) {
	_, err := r.client.HeadObject(ctx, &s3.HeadObjectInput{
		Bucket: aws.String(r.bucketName),
		Key:    aws.String(key),
	})
	if err != nil {
		if strings.Contains(err.Error(), "NotFound") || strings.Contains(err.Error(), "NoSuchKey") {
			return false, nil
		}
		return false, fmt.Errorf("erro ao verificar existência do arquivo: %v", err)
	}
	return true, nil
}

// buildPublicURL constrói a URL pública para um arquivo
func (r *R2Client) buildPublicURL(key string) string {
	if r.config.PublicURL != "" {
		return fmt.Sprintf("%s/%s", strings.TrimRight(r.config.PublicURL, "/"), key)
	}

	// URL padrão do R2 (pode não ser pública dependendo das configurações do bucket)
	return fmt.Sprintf("https://%s.r2.cloudflarestorage.com/%s", r.bucketName, key)
}

// GetPublicURL retorna a URL pública de um arquivo
func (r *R2Client) GetPublicURL(key string) string {
	return r.buildPublicURL(key)
}
