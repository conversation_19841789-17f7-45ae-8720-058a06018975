package storage

import (
	"fmt"
	"log"
	"strings"
)

// StorageType define os tipos de armazenamento disponíveis
type StorageType string

const (
	StorageTypeLocal StorageType = "local"
	StorageTypeR2    StorageType = "r2"
)

// StorageConfig contém configurações gerais de armazenamento
type StorageConfig struct {
	Type StorageType `json:"type"`
	
	// Configurações para armazenamento local
	LocalConfig *LocalConfig `json:"local_config,omitempty"`
	
	// Configurações para R2
	R2Config *R2Config `json:"r2_config,omitempty"`
}

// NewStorageProvider cria um provedor de armazenamento baseado na configuração
func NewStorageProvider(config *StorageConfig) (StorageProvider, error) {
	if config == nil {
		return nil, fmt.Errorf("configuração de armazenamento é obrigatória")
	}

	switch config.Type {
	case StorageTypeLocal:
		if config.LocalConfig == nil {
			return nil, fmt.Errorf("configuração local é obrigatória para tipo 'local'")
		}
		return NewLocalClient(config.LocalConfig)
		
	case StorageTypeR2:
		if config.R2Config == nil {
			return nil, fmt.Errorf("configuração R2 é obrigatória para tipo 'r2'")
		}
		return NewR2Client(config.R2Config)
		
	default:
		return nil, fmt.Errorf("tipo de armazenamento não suportado: %s", config.Type)
	}
}

// NewStorageProviderFromEnv cria um provedor baseado em variáveis de ambiente
func NewStorageProviderFromEnv(
	storageType string,
	// Variáveis para R2
	r2Endpoint, r2AccessKey, r2SecretKey, r2BucketName, r2PublicURL string,
	// Variáveis para Local
	localBaseDir, localBaseURL string,
) (StorageProvider, error) {
	
	// Normalizar tipo
	storageType = strings.ToLower(strings.TrimSpace(storageType))
	
	switch storageType {
	case "local", "":
		// Usar armazenamento local como padrão
		if localBaseDir == "" {
			localBaseDir = "./storage"
		}
		if localBaseURL == "" {
			localBaseURL = "/files"
		}
		
		config := &StorageConfig{
			Type: StorageTypeLocal,
			LocalConfig: &LocalConfig{
				BaseDir:   localBaseDir,
				BaseURL:   localBaseURL,
				CreateDir: true,
			},
		}
		
		log.Printf("🏠 Usando armazenamento local: %s", localBaseDir)
		return NewStorageProvider(config)
		
	case "r2":
		// Validar configurações R2
		if r2Endpoint == "" || r2AccessKey == "" || r2SecretKey == "" || r2BucketName == "" {
			return nil, fmt.Errorf("configurações R2 incompletas: endpoint, access_key, secret_key e bucket_name são obrigatórios")
		}
		
		config := &StorageConfig{
			Type: StorageTypeR2,
			R2Config: &R2Config{
				Endpoint:   r2Endpoint,
				AccessKey:  r2AccessKey,
				SecretKey:  r2SecretKey,
				BucketName: r2BucketName,
				Region:     "auto",
				PublicURL:  r2PublicURL,
			},
		}
		
		log.Printf("☁️  Usando Cloudflare R2: %s", r2BucketName)
		return NewStorageProvider(config)
		
	default:
		return nil, fmt.Errorf("tipo de armazenamento não suportado: %s (use 'local' ou 'r2')", storageType)
	}
}

// ValidateStorageConfig valida uma configuração de armazenamento
func ValidateStorageConfig(config *StorageConfig) error {
	if config == nil {
		return fmt.Errorf("configuração não pode ser nil")
	}
	
	switch config.Type {
	case StorageTypeLocal:
		if config.LocalConfig == nil {
			return fmt.Errorf("LocalConfig é obrigatório para tipo local")
		}
		if config.LocalConfig.BaseDir == "" {
			return fmt.Errorf("BaseDir é obrigatório para armazenamento local")
		}
		
	case StorageTypeR2:
		if config.R2Config == nil {
			return fmt.Errorf("R2Config é obrigatório para tipo R2")
		}
		r2 := config.R2Config
		if r2.Endpoint == "" || r2.AccessKey == "" || r2.SecretKey == "" || r2.BucketName == "" {
			return fmt.Errorf("configurações R2 incompletas: endpoint, access_key, secret_key e bucket_name são obrigatórios")
		}
		
	default:
		return fmt.Errorf("tipo de armazenamento não suportado: %s", config.Type)
	}
	
	return nil
}
