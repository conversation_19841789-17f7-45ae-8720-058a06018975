package queue

import (
	"encoding/json"
	"sync"
	"time"
)

// Metrics contém métricas do sistema de filas
type Metrics struct {
	mu                    sync.RWMutex
	JobsEnqueued          int64                  `json:"jobs_enqueued"`
	JobsProcessed         int64                  `json:"jobs_processed"`
	JobsSucceeded         int64                  `json:"jobs_succeeded"`
	JobsFailed            int64                  `json:"jobs_failed"`
	JobsRetried           int64                  `json:"jobs_retried"`
	TotalProcessingTime   time.Duration          `json:"total_processing_time"`
	AverageProcessingTime time.Duration          `json:"average_processing_time"`
	StartTime             time.Time              `json:"start_time"`
	LastJobAt             time.Time              `json:"last_job_at"`
	ProcessingTimeByType  map[string]time.Duration `json:"processing_time_by_type"`
	ErrorsByType          map[string]int64       `json:"errors_by_type"`
}

// MetricsCollector coleta e agrega métricas
type MetricsCollector struct {
	metrics *Metrics
}

// NewMetricsCollector cria um novo coletor de métricas
func NewMetricsCollector() *MetricsCollector {
	return &MetricsCollector{
		metrics: &Metrics{
			StartTime:            time.Now(),
			ProcessingTimeByType: make(map[string]time.Duration),
			ErrorsByType:         make(map[string]int64),
		},
	}
}

// RecordJobEnqueued registra um job enfileirado
func (mc *MetricsCollector) RecordJobEnqueued() {
	mc.metrics.mu.Lock()
	defer mc.metrics.mu.Unlock()
	
	mc.metrics.JobsEnqueued++
	mc.metrics.LastJobAt = time.Now()
}

// RecordJobProcessed registra um job processado
func (mc *MetricsCollector) RecordJobProcessed(processingTime time.Duration, success bool) {
	mc.metrics.mu.Lock()
	defer mc.metrics.mu.Unlock()
	
	mc.metrics.JobsProcessed++
	mc.metrics.TotalProcessingTime += processingTime
	mc.metrics.LastJobAt = time.Now()
	
	if success {
		mc.metrics.JobsSucceeded++
	} else {
		mc.metrics.JobsFailed++
	}
	
	// Calcular tempo médio
	if mc.metrics.JobsProcessed > 0 {
		mc.metrics.AverageProcessingTime = mc.metrics.TotalProcessingTime / time.Duration(mc.metrics.JobsProcessed)
	}
}

// RecordJobRetried registra uma tentativa de retry
func (mc *MetricsCollector) RecordJobRetried() {
	mc.metrics.mu.Lock()
	defer mc.metrics.mu.Unlock()
	
	mc.metrics.JobsRetried++
}

// RecordError registra um erro por tipo
func (mc *MetricsCollector) RecordError(errorType string) {
	mc.metrics.mu.Lock()
	defer mc.metrics.mu.Unlock()
	
	mc.metrics.ErrorsByType[errorType]++
}

// RecordProcessingTimeByType registra tempo de processamento por tipo
func (mc *MetricsCollector) RecordProcessingTimeByType(jobType string, processingTime time.Duration) {
	mc.metrics.mu.Lock()
	defer mc.metrics.mu.Unlock()
	
	mc.metrics.ProcessingTimeByType[jobType] += processingTime
}

// GetMetrics retorna uma cópia das métricas atuais
func (mc *MetricsCollector) GetMetrics() *Metrics {
	mc.metrics.mu.RLock()
	defer mc.metrics.mu.RUnlock()
	
	// Criar cópia para evitar race conditions
	metrics := &Metrics{
		JobsEnqueued:          mc.metrics.JobsEnqueued,
		JobsProcessed:         mc.metrics.JobsProcessed,
		JobsSucceeded:         mc.metrics.JobsSucceeded,
		JobsFailed:            mc.metrics.JobsFailed,
		JobsRetried:           mc.metrics.JobsRetried,
		TotalProcessingTime:   mc.metrics.TotalProcessingTime,
		AverageProcessingTime: mc.metrics.AverageProcessingTime,
		StartTime:             mc.metrics.StartTime,
		LastJobAt:             mc.metrics.LastJobAt,
		ProcessingTimeByType:  make(map[string]time.Duration),
		ErrorsByType:          make(map[string]int64),
	}
	
	// Copiar maps
	for k, v := range mc.metrics.ProcessingTimeByType {
		metrics.ProcessingTimeByType[k] = v
	}
	for k, v := range mc.metrics.ErrorsByType {
		metrics.ErrorsByType[k] = v
	}
	
	return metrics
}

// GetMetricsJSON retorna métricas em formato JSON
func (mc *MetricsCollector) GetMetricsJSON() ([]byte, error) {
	metrics := mc.GetMetrics()
	return json.Marshal(metrics)
}

// Reset reseta todas as métricas
func (mc *MetricsCollector) Reset() {
	mc.metrics.mu.Lock()
	defer mc.metrics.mu.Unlock()
	
	mc.metrics.JobsEnqueued = 0
	mc.metrics.JobsProcessed = 0
	mc.metrics.JobsSucceeded = 0
	mc.metrics.JobsFailed = 0
	mc.metrics.JobsRetried = 0
	mc.metrics.TotalProcessingTime = 0
	mc.metrics.AverageProcessingTime = 0
	mc.metrics.StartTime = time.Now()
	mc.metrics.LastJobAt = time.Time{}
	mc.metrics.ProcessingTimeByType = make(map[string]time.Duration)
	mc.metrics.ErrorsByType = make(map[string]int64)
}

// GetUptime retorna o tempo de atividade
func (mc *MetricsCollector) GetUptime() time.Duration {
	mc.metrics.mu.RLock()
	defer mc.metrics.mu.RUnlock()
	
	return time.Since(mc.metrics.StartTime)
}

// GetThroughput retorna jobs processados por minuto
func (mc *MetricsCollector) GetThroughput() float64 {
	mc.metrics.mu.RLock()
	defer mc.metrics.mu.RUnlock()
	
	uptime := time.Since(mc.metrics.StartTime)
	if uptime.Minutes() == 0 {
		return 0
	}
	
	return float64(mc.metrics.JobsProcessed) / uptime.Minutes()
}

// GetSuccessRate retorna a taxa de sucesso em porcentagem
func (mc *MetricsCollector) GetSuccessRate() float64 {
	mc.metrics.mu.RLock()
	defer mc.metrics.mu.RUnlock()
	
	if mc.metrics.JobsProcessed == 0 {
		return 0
	}
	
	return (float64(mc.metrics.JobsSucceeded) / float64(mc.metrics.JobsProcessed)) * 100
}

// GetDetailedStats retorna estatísticas detalhadas
func (mc *MetricsCollector) GetDetailedStats() map[string]interface{} {
	metrics := mc.GetMetrics()
	
	return map[string]interface{}{
		"jobs": map[string]interface{}{
			"enqueued":  metrics.JobsEnqueued,
			"processed": metrics.JobsProcessed,
			"succeeded": metrics.JobsSucceeded,
			"failed":    metrics.JobsFailed,
			"retried":   metrics.JobsRetried,
		},
		"performance": map[string]interface{}{
			"total_processing_time":   metrics.TotalProcessingTime.String(),
			"average_processing_time": metrics.AverageProcessingTime.String(),
			"throughput_per_minute":   mc.GetThroughput(),
			"success_rate_percent":    mc.GetSuccessRate(),
		},
		"timing": map[string]interface{}{
			"start_time":    metrics.StartTime.Format(time.RFC3339),
			"last_job_at":   metrics.LastJobAt.Format(time.RFC3339),
			"uptime":        mc.GetUptime().String(),
		},
		"processing_time_by_type": metrics.ProcessingTimeByType,
		"errors_by_type":          metrics.ErrorsByType,
	}
}

// LogEvent registra um evento estruturado
type LogEvent struct {
	Timestamp time.Time              `json:"timestamp"`
	Level     string                 `json:"level"`
	Message   string                 `json:"message"`
	JobID     string                 `json:"job_id,omitempty"`
	WorkerID  string                 `json:"worker_id,omitempty"`
	Duration  time.Duration          `json:"duration,omitempty"`
	Error     string                 `json:"error,omitempty"`
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
}

// Logger estruturado para o sistema de filas
type Logger struct {
	events []LogEvent
	mu     sync.RWMutex
}

// NewLogger cria um novo logger estruturado
func NewLogger() *Logger {
	return &Logger{
		events: make([]LogEvent, 0),
	}
}

// LogInfo registra um evento de informação
func (l *Logger) LogInfo(message string, jobID, workerID string, metadata map[string]interface{}) {
	l.logEvent("INFO", message, jobID, workerID, 0, "", metadata)
}

// LogError registra um evento de erro
func (l *Logger) LogError(message string, jobID, workerID string, err error, metadata map[string]interface{}) {
	errorMsg := ""
	if err != nil {
		errorMsg = err.Error()
	}
	l.logEvent("ERROR", message, jobID, workerID, 0, errorMsg, metadata)
}

// LogWarning registra um evento de aviso
func (l *Logger) LogWarning(message string, jobID, workerID string, metadata map[string]interface{}) {
	l.logEvent("WARNING", message, jobID, workerID, 0, "", metadata)
}

// LogPerformance registra um evento de performance
func (l *Logger) LogPerformance(message string, jobID, workerID string, duration time.Duration, metadata map[string]interface{}) {
	l.logEvent("PERFORMANCE", message, jobID, workerID, duration, "", metadata)
}

// logEvent registra um evento interno
func (l *Logger) logEvent(level, message, jobID, workerID string, duration time.Duration, errorMsg string, metadata map[string]interface{}) {
	l.mu.Lock()
	defer l.mu.Unlock()
	
	event := LogEvent{
		Timestamp: time.Now(),
		Level:     level,
		Message:   message,
		JobID:     jobID,
		WorkerID:  workerID,
		Duration:  duration,
		Error:     errorMsg,
		Metadata:  metadata,
	}
	
	l.events = append(l.events, event)
	
	// Manter apenas os últimos 1000 eventos
	if len(l.events) > 1000 {
		l.events = l.events[len(l.events)-1000:]
	}
}

// GetRecentEvents retorna eventos recentes
func (l *Logger) GetRecentEvents(limit int) []LogEvent {
	l.mu.RLock()
	defer l.mu.RUnlock()
	
	if limit <= 0 || limit > len(l.events) {
		limit = len(l.events)
	}
	
	start := len(l.events) - limit
	if start < 0 {
		start = 0
	}
	
	events := make([]LogEvent, limit)
	copy(events, l.events[start:])
	
	return events
}

// GetEventsJSON retorna eventos em formato JSON
func (l *Logger) GetEventsJSON(limit int) ([]byte, error) {
	events := l.GetRecentEvents(limit)
	return json.Marshal(events)
}
