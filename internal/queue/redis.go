package queue

import (
	"context"
	"fmt"
	"log"
	"os"
	"strconv"
	"time"

	"github.com/redis/go-redis/v9"
)

// RedisConfig contém as configurações do Redis
type RedisConfig struct {
	Host         string        `json:"host"`
	Port         string        `json:"port"`
	Password     string        `json:"password"`
	DB           int           `json:"db"`
	PoolSize     int           `json:"pool_size"`
	DialTimeout  time.Duration `json:"dial_timeout"`
	ReadTimeout  time.Duration `json:"read_timeout"`
	WriteTimeout time.Duration `json:"write_timeout"`
}

// RedisClient wrapper para o cliente Redis
type RedisClient struct {
	client *redis.Client
	config *RedisConfig
}

// NewRedisClient cria uma nova instância do cliente Redis
func NewRedisClient(config *RedisConfig) (*RedisClient, error) {
	if config == nil {
		return nil, fmt.Errorf("configuração Redis é obrigatória")
	}

	// Configurações padrão
	if config.PoolSize == 0 {
		config.PoolSize = 10
	}
	if config.DialTimeout == 0 {
		config.DialTimeout = 5 * time.Second
	}
	if config.ReadTimeout == 0 {
		config.ReadTimeout = 3 * time.Second
	}
	if config.WriteTimeout == 0 {
		config.WriteTimeout = 3 * time.Second
	}

	// Criar cliente Redis
	rdb := redis.NewClient(&redis.Options{
		Addr:         fmt.Sprintf("%s:%s", config.Host, config.Port),
		Password:     config.Password,
		DB:           config.DB,
		PoolSize:     config.PoolSize,
		DialTimeout:  config.DialTimeout,
		ReadTimeout:  config.ReadTimeout,
		WriteTimeout: config.WriteTimeout,
	})

	// Testar conexão
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := rdb.Ping(ctx).Err(); err != nil {
		return nil, fmt.Errorf("falha ao conectar com Redis: %v", err)
	}

	log.Printf("✅ Conectado ao Redis em %s:%s", config.Host, config.Port)

	return &RedisClient{
		client: rdb,
		config: config,
	}, nil
}

// GetClient retorna o cliente Redis nativo
func (r *RedisClient) GetClient() *redis.Client {
	return r.client
}

// Close fecha a conexão com Redis
func (r *RedisClient) Close() error {
	return r.client.Close()
}

// Ping testa a conexão com Redis
func (r *RedisClient) Ping(ctx context.Context) error {
	return r.client.Ping(ctx).Err()
}

// Health verifica a saúde da conexão Redis
func (r *RedisClient) Health(ctx context.Context) map[string]interface{} {
	health := map[string]interface{}{
		"redis_connected": false,
		"redis_host":      fmt.Sprintf("%s:%s", r.config.Host, r.config.Port),
		"redis_db":        r.config.DB,
	}

	if err := r.Ping(ctx); err != nil {
		health["redis_error"] = err.Error()
	} else {
		health["redis_connected"] = true

		// Informações adicionais se conectado
		info := r.client.Info(ctx, "server").Val()
		if info != "" {
			health["redis_info"] = "connected"
		}
	}

	return health
}

// NewRedisClientFromEnv cria cliente Redis a partir de variáveis de ambiente
func NewRedisClientFromEnv() (*RedisClient, error) {
	config := &RedisConfig{
		Host:         getEnv("REDIS_HOST", "localhost"),
		Port:         getEnv("REDIS_PORT", "6379"),
		Password:     getEnv("REDIS_PASSWORD", ""),
		DB:           getEnvInt("REDIS_DB", 0),
		PoolSize:     getEnvInt("REDIS_POOL_SIZE", 10),
		DialTimeout:  getEnvDuration("REDIS_DIAL_TIMEOUT", 5*time.Second),
		ReadTimeout:  getEnvDuration("REDIS_READ_TIMEOUT", 3*time.Second),
		WriteTimeout: getEnvDuration("REDIS_WRITE_TIMEOUT", 3*time.Second),
	}

	return NewRedisClient(config)
}

// Funções auxiliares para variáveis de ambiente
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func getEnvInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if parsed, err := strconv.Atoi(value); err == nil {
			return parsed
		}
	}
	return defaultValue
}

func getEnvDuration(key string, defaultValue time.Duration) time.Duration {
	if value := os.Getenv(key); value != "" {
		if parsed, err := time.ParseDuration(value); err == nil {
			return parsed
		}
	}
	return defaultValue
}
