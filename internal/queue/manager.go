package queue

import (
	"context"
	"fmt"
	"log"
	"time"

	"github.com/redis/go-redis/v9"
)

// QueueConfig contém configurações da fila
type QueueConfig struct {
	DefaultTimeout    time.Duration `json:"default_timeout"`
	RetryDelay        time.Duration `json:"retry_delay"`
	MaxRetries        int           `json:"max_retries"`
	DeadLetterEnabled bool          `json:"dead_letter_enabled"`
	CleanupInterval   time.Duration `json:"cleanup_interval"`
}

// QueueManager gerencia as operações de fila
type QueueManager struct {
	redis   *RedisClient
	config  *QueueConfig
	metrics *MetricsCollector
	logger  *Logger
}

// QueueNames define os nomes das filas
var QueueNames = struct {
	High       string
	Normal     string
	Low        string
	Processing string
	DeadLetter string
	Completed  string
	Failed     string
}{
	High:       "video_processing:high",
	Normal:     "video_processing:normal",
	Low:        "video_processing:low",
	Processing: "video_processing:processing",
	DeadLetter: "video_processing:dlq",
	Completed:  "video_processing:completed",
	Failed:     "video_processing:failed",
}

// NewQueueManager cria um novo gerenciador de filas
func NewQueueManager(redisClient *RedisClient, config *QueueConfig) *QueueManager {
	if config == nil {
		config = &QueueConfig{
			DefaultTimeout:    300 * time.Second, // 5 minutos
			RetryDelay:        30 * time.Second,
			MaxRetries:        3,
			DeadLetterEnabled: true,
			CleanupInterval:   60 * time.Second, // 1 minuto
		}
	}

	return &QueueManager{
		redis:   redisClient,
		config:  config,
		metrics: NewMetricsCollector(),
		logger:  NewLogger(),
	}
}

// Enqueue adiciona um job à fila
func (qm *QueueManager) Enqueue(ctx context.Context, job *VideoJob) error {
	// Serializar job para JSON
	jobData, err := job.ToJSON()
	if err != nil {
		return fmt.Errorf("erro ao serializar job: %v", err)
	}

	// Determinar fila baseada na prioridade
	queueKey := job.GetQueueKey()

	// Adicionar à fila
	if err := qm.redis.GetClient().LPush(ctx, queueKey, jobData).Err(); err != nil {
		qm.logger.LogError("Erro ao adicionar job à fila", job.ID, "", err, map[string]interface{}{
			"queue": queueKey,
		})
		return fmt.Errorf("erro ao adicionar job à fila: %v", err)
	}

	// Salvar metadados do job
	if err := qm.saveJobMetadata(ctx, job); err != nil {
		qm.logger.LogWarning("Erro ao salvar metadados do job", job.ID, "", map[string]interface{}{
			"error": err.Error(),
		})
		log.Printf("Aviso: erro ao salvar metadados do job %s: %v", job.ID, err)
	}

	// Registrar métricas
	qm.metrics.RecordJobEnqueued()

	qm.logger.LogInfo("Job adicionado à fila", job.ID, "", map[string]interface{}{
		"queue":    queueKey,
		"priority": job.Priority,
		"filename": job.Metadata.OriginalName,
		"size":     job.Metadata.FileSize,
	})

	log.Printf("Job %s adicionado à fila %s", job.ID, queueKey)
	return nil
}

// Dequeue remove e retorna um job da fila
func (qm *QueueManager) Dequeue(ctx context.Context) (*VideoJob, error) {
	// Tentar filas por ordem de prioridade
	queues := []string{QueueNames.High, QueueNames.Normal, QueueNames.Low}

	for _, queueKey := range queues {
		// Usar BRPOPLPUSH para mover atomicamente da fila para processing
		result := qm.redis.GetClient().BRPopLPush(ctx, queueKey, QueueNames.Processing, time.Second)
		if result.Err() != nil {
			if result.Err() == redis.Nil {
				continue // Fila vazia, tentar próxima
			}
			return nil, fmt.Errorf("erro ao fazer dequeue: %v", result.Err())
		}

		// Deserializar job
		job, err := FromJSON([]byte(result.Val()))
		if err != nil {
			log.Printf("Erro ao deserializar job: %v", err)
			continue
		}

		// Atualizar status para processing
		job.UpdateStatus(JobStatusProcessing)

		// Salvar estado atualizado
		if err := qm.saveJobMetadata(ctx, job); err != nil {
			log.Printf("Aviso: erro ao atualizar metadados do job %s: %v", job.ID, err)
		}

		log.Printf("Job %s removido da fila %s para processamento", job.ID, queueKey)
		return job, nil
	}

	return nil, nil // Nenhum job disponível
}

// CompleteJob marca um job como concluído
func (qm *QueueManager) CompleteJob(ctx context.Context, job *VideoJob) error {
	// Atualizar status
	job.UpdateStatus(JobStatusCompleted)

	// Remover da fila de processamento
	if err := qm.removeFromProcessing(ctx, job); err != nil {
		log.Printf("Aviso: erro ao remover job da fila de processamento: %v", err)
	}

	// Adicionar à fila de concluídos
	jobData, _ := job.ToJSON()
	if err := qm.redis.GetClient().LPush(ctx, QueueNames.Completed, jobData).Err(); err != nil {
		log.Printf("Aviso: erro ao adicionar job à fila de concluídos: %v", err)
	}

	// Salvar metadados atualizados
	if err := qm.saveJobMetadata(ctx, job); err != nil {
		log.Printf("Aviso: erro ao salvar metadados do job: %v", err)
	}

	log.Printf("Job %s marcado como concluído", job.ID)
	return nil
}

// FailJob marca um job como falhado e tenta reprocessar se possível
func (qm *QueueManager) FailJob(ctx context.Context, job *VideoJob, err error) error {
	job.SetError(err)
	job.IncrementAttempts()

	// Remover da fila de processamento
	if removeErr := qm.removeFromProcessing(ctx, job); removeErr != nil {
		log.Printf("Aviso: erro ao remover job da fila de processamento: %v", removeErr)
	}

	// Verificar se pode tentar novamente
	if job.CanRetry() {
		job.UpdateStatus(JobStatusRetrying)

		// Reagendar para retry após delay
		go func() {
			time.Sleep(job.Metadata.RetryDelay)
			if retryErr := qm.Enqueue(context.Background(), job); retryErr != nil {
				log.Printf("Erro ao reagendar job %s: %v", job.ID, retryErr)
				// Se falhar ao reagendar, mover para DLQ
				qm.moveToDeadLetter(context.Background(), job)
			}
		}()

		log.Printf("Job %s reagendado para retry (tentativa %d/%d)",
			job.ID, job.Metadata.Attempts, job.Metadata.MaxAttempts)
	} else {
		// Mover para dead letter queue
		job.UpdateStatus(JobStatusFailed)
		if dlqErr := qm.moveToDeadLetter(ctx, job); dlqErr != nil {
			log.Printf("Erro ao mover job para DLQ: %v", dlqErr)
		}

		log.Printf("Job %s movido para dead letter queue após %d tentativas",
			job.ID, job.Metadata.Attempts)
	}

	// Salvar metadados atualizados
	if saveErr := qm.saveJobMetadata(ctx, job); saveErr != nil {
		log.Printf("Aviso: erro ao salvar metadados do job: %v", saveErr)
	}

	return nil
}

// GetJobStatus retorna o status de um job
func (qm *QueueManager) GetJobStatus(ctx context.Context, jobID string) (*VideoJob, error) {
	key := fmt.Sprintf("job:%s", jobID)
	result := qm.redis.GetClient().Get(ctx, key)

	if result.Err() != nil {
		if result.Err() == redis.Nil {
			return nil, fmt.Errorf("job não encontrado")
		}
		return nil, fmt.Errorf("erro ao buscar job: %v", result.Err())
	}

	job, err := FromJSON([]byte(result.Val()))
	if err != nil {
		return nil, fmt.Errorf("erro ao deserializar job: %v", err)
	}

	return job, nil
}

// GetQueueStats retorna estatísticas das filas
func (qm *QueueManager) GetQueueStats(ctx context.Context) (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// Contar jobs em cada fila
	queues := map[string]string{
		"high":        QueueNames.High,
		"normal":      QueueNames.Normal,
		"low":         QueueNames.Low,
		"processing":  QueueNames.Processing,
		"completed":   QueueNames.Completed,
		"failed":      QueueNames.Failed,
		"dead_letter": QueueNames.DeadLetter,
	}

	for name, key := range queues {
		count := qm.redis.GetClient().LLen(ctx, key).Val()
		stats[name] = count
	}

	// Total de jobs
	total := stats["high"].(int64) + stats["normal"].(int64) + stats["low"].(int64)
	stats["total_pending"] = total
	stats["total_processing"] = stats["processing"]
	stats["total_completed"] = stats["completed"]
	stats["total_failed"] = stats["failed"]

	return stats, nil
}

// saveJobMetadata salva os metadados do job no Redis
func (qm *QueueManager) saveJobMetadata(ctx context.Context, job *VideoJob) error {
	key := fmt.Sprintf("job:%s", job.ID)
	jobData, err := job.ToJSON()
	if err != nil {
		return err
	}

	// Salvar com TTL de 24 horas
	return qm.redis.GetClient().Set(ctx, key, jobData, 24*time.Hour).Err()
}

// removeFromProcessing remove um job da fila de processamento
func (qm *QueueManager) removeFromProcessing(ctx context.Context, job *VideoJob) error {
	// Primeiro, tentar remover usando a serialização atual do job
	jobData, err := job.ToJSON()
	if err != nil {
		return err
	}

	// Tentar remoção direta primeiro
	removed := qm.redis.GetClient().LRem(ctx, QueueNames.Processing, 1, jobData).Val()
	if removed > 0 {
		log.Printf("Job %s removido da fila de processamento (método direto)", job.ID)
		return nil
	}

	// Se não conseguiu remover diretamente, buscar por ID na fila
	log.Printf("Tentativa de remoção direta falhou para job %s, buscando por ID", job.ID)

	// Obter todos os jobs da fila de processamento
	jobs := qm.redis.GetClient().LRange(ctx, QueueNames.Processing, 0, -1)
	if jobs.Err() != nil {
		return jobs.Err()
	}

	// Procurar o job pelo ID e remover
	for i, jobDataStr := range jobs.Val() {
		tempJob, err := FromJSON([]byte(jobDataStr))
		if err != nil {
			continue
		}

		if tempJob.ID == job.ID {
			// Remover usando o índice
			pipe := qm.redis.GetClient().TxPipeline()
			pipe.LSet(ctx, QueueNames.Processing, int64(i), "__DELETED__")
			pipe.LRem(ctx, QueueNames.Processing, 1, "__DELETED__")
			_, err := pipe.Exec(ctx)

			if err != nil {
				log.Printf("Erro ao remover job %s da fila de processamento: %v", job.ID, err)
				return err
			}

			log.Printf("Job %s removido da fila de processamento (método por ID)", job.ID)
			return nil
		}
	}

	log.Printf("Aviso: Job %s não encontrado na fila de processamento", job.ID)
	return nil // Não retornar erro se o job não estiver na fila
}

// moveToDeadLetter move um job para a dead letter queue
func (qm *QueueManager) moveToDeadLetter(ctx context.Context, job *VideoJob) error {
	if !qm.config.DeadLetterEnabled {
		return nil
	}

	jobData, err := job.ToJSON()
	if err != nil {
		return err
	}

	return qm.redis.GetClient().LPush(ctx, QueueNames.DeadLetter, jobData).Err()
}

// CleanupExpiredJobs remove jobs expirados da fila de processamento
func (qm *QueueManager) CleanupExpiredJobs(ctx context.Context) error {
	// Obter todos os jobs da fila de processamento
	jobs := qm.redis.GetClient().LRange(ctx, QueueNames.Processing, 0, -1)
	if jobs.Err() != nil {
		return jobs.Err()
	}

	expiredCount := 0
	orphanedCount := 0

	for i, jobData := range jobs.Val() {
		job, err := FromJSON([]byte(jobData))
		if err != nil {
			// Job corrompido, remover
			qm.redis.GetClient().LSet(ctx, QueueNames.Processing, int64(i), "__CORRUPTED__")
			qm.redis.GetClient().LRem(ctx, QueueNames.Processing, 1, "__CORRUPTED__")
			orphanedCount++
			continue
		}

		// Verificar se o job expirou
		if job.IsExpired() {
			// Remover da fila de processamento usando método melhorado
			if err := qm.removeFromProcessing(ctx, job); err != nil {
				log.Printf("Erro ao remover job expirado %s: %v", job.ID, err)
			}

			// Marcar como falhado e tentar reprocessar
			qm.FailJob(ctx, job, fmt.Errorf("job expirou após %v", qm.config.DefaultTimeout))
			expiredCount++
		}
	}

	if expiredCount > 0 || orphanedCount > 0 {
		log.Printf("Limpeza: %d jobs expirados e %d jobs corrompidos removidos da fila de processamento",
			expiredCount, orphanedCount)
	}

	return nil
}

// CleanupOrphanedJobs remove jobs órfãos da fila de processamento
func (qm *QueueManager) CleanupOrphanedJobs(ctx context.Context) error {
	// Obter todos os jobs da fila de processamento
	jobs := qm.redis.GetClient().LRange(ctx, QueueNames.Processing, 0, -1)
	if jobs.Err() != nil {
		return jobs.Err()
	}

	cleanedCount := 0
	validJobs := make([]string, 0)

	for _, jobData := range jobs.Val() {
		job, err := FromJSON([]byte(jobData))
		if err != nil {
			// Job corrompido, não adicionar à lista de válidos
			cleanedCount++
			log.Printf("Job corrompido removido da fila de processamento")
			continue
		}

		// Verificar se o job tem metadados válidos
		if job.ID == "" || job.Status == "" {
			cleanedCount++
			log.Printf("Job inválido removido da fila de processamento: %+v", job)
			continue
		}

		// Job válido, manter
		validJobs = append(validJobs, jobData)
	}

	// Se encontrou jobs para limpar, recriar a fila
	if cleanedCount > 0 {
		// Limpar a fila completamente
		if err := qm.redis.GetClient().Del(ctx, QueueNames.Processing).Err(); err != nil {
			return fmt.Errorf("erro ao limpar fila de processamento: %v", err)
		}

		// Readicionar apenas os jobs válidos
		if len(validJobs) > 0 {
			args := make([]interface{}, len(validJobs))
			for i, job := range validJobs {
				args[i] = job
			}

			if err := qm.redis.GetClient().RPush(ctx, QueueNames.Processing, args...).Err(); err != nil {
				return fmt.Errorf("erro ao readicionar jobs válidos: %v", err)
			}
		}

		log.Printf("Limpeza de órfãos: %d jobs inválidos removidos, %d jobs válidos mantidos",
			cleanedCount, len(validJobs))
	}

	return nil
}

// StartCleanupWorker inicia um worker para limpeza periódica
func (qm *QueueManager) StartCleanupWorker(ctx context.Context) {
	ticker := time.NewTicker(qm.config.CleanupInterval)
	defer ticker.Stop()

	log.Printf("Worker de limpeza iniciado (intervalo: %v)", qm.config.CleanupInterval)

	cleanupCount := 0
	for {
		select {
		case <-ctx.Done():
			log.Println("Worker de limpeza finalizado")
			return
		case <-ticker.C:
			cleanupCount++

			// Limpeza de jobs expirados a cada ciclo
			if err := qm.CleanupExpiredJobs(ctx); err != nil {
				log.Printf("Erro na limpeza de jobs expirados: %v", err)
			}

			// Limpeza de jobs órfãos a cada 5 ciclos (menos frequente)
			if cleanupCount%5 == 0 {
				if err := qm.CleanupOrphanedJobs(ctx); err != nil {
					log.Printf("Erro na limpeza de jobs órfãos: %v", err)
				}
			}
		}
	}
}

// PurgeQueue limpa completamente uma fila
func (qm *QueueManager) PurgeQueue(ctx context.Context, queueName string) error {
	count := qm.redis.GetClient().LLen(ctx, queueName).Val()
	if count == 0 {
		return nil
	}

	err := qm.redis.GetClient().Del(ctx, queueName).Err()
	if err != nil {
		return fmt.Errorf("erro ao limpar fila %s: %v", queueName, err)
	}

	log.Printf("Fila %s limpa (%d jobs removidos)", queueName, count)
	return nil
}

// ListJobs lista jobs de uma fila específica
func (qm *QueueManager) ListJobs(ctx context.Context, queueName string, start, stop int64) ([]*VideoJob, error) {
	jobsData := qm.redis.GetClient().LRange(ctx, queueName, start, stop)
	if jobsData.Err() != nil {
		return nil, jobsData.Err()
	}

	var jobs []*VideoJob
	for _, data := range jobsData.Val() {
		job, err := FromJSON([]byte(data))
		if err != nil {
			log.Printf("Erro ao deserializar job: %v", err)
			continue
		}
		jobs = append(jobs, job)
	}

	return jobs, nil
}

// GetMetrics retorna métricas do queue manager
func (qm *QueueManager) GetMetrics() *Metrics {
	return qm.metrics.GetMetrics()
}

// GetLogger retorna o logger do queue manager
func (qm *QueueManager) GetLogger() *Logger {
	return qm.logger
}

// GetDetailedMetrics retorna métricas detalhadas
func (qm *QueueManager) GetDetailedMetrics() map[string]interface{} {
	return qm.metrics.GetDetailedStats()
}
