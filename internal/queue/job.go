package queue

import (
	"crypto/rand"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"time"
)

// JobStatus representa o status de um job
type JobStatus string

const (
	JobStatusPending    JobStatus = "pending"
	JobStatusProcessing JobStatus = "processing"
	JobStatusCompleted  JobStatus = "completed"
	JobStatusFailed     JobStatus = "failed"
	JobStatusRetrying   JobStatus = "retrying"
	JobStatusCancelled  JobStatus = "cancelled"
)

// JobPriority representa a prioridade de um job
type JobPriority int

const (
	PriorityLow    JobPriority = 1
	PriorityNormal JobPriority = 5
	PriorityHigh   JobPriority = 10
)

// QualityConfig define configurações para uma qualidade específica
type QualityConfig struct {
	Name      string `json:"name"`       // Nome da qualidade (480p, 720p, 1080p)
	Width     int    `json:"width"`      // Largura em pixels
	Height    int    `json:"height"`     // Altura em pixels
	Bitrate   int    `json:"bitrate"`    // Bitrate de vídeo em kbps
	AudioRate int    `json:"audio_rate"` // Bitrate de áudio em kbps
	MaxRate   int    `json:"max_rate"`   // Bitrate máximo em kbps
	BufSize   int    `json:"buf_size"`   // Buffer size em kbps
}

// VideoProcessingConfig contém configurações específicas para processamento de vídeo
type VideoProcessingConfig struct {
	SegmentTime       int             `json:"segment_time"`     // Duração de cada segmento HLS em segundos
	PlaylistType      string          `json:"playlist_type"`    // Tipo de playlist HLS (vod ou live)
	VideoCodec        string          `json:"video_codec"`      // Codec de vídeo (h264, etc.)
	AudioCodec        string          `json:"audio_codec"`      // Codec de áudio (aac, etc.)
	VideoBitrates     []int           `json:"video_bitrates"`   // Bitrates para streaming adaptativo (deprecated)
	Qualities         []QualityConfig `json:"qualities"`        // Configurações de qualidades
	EnableStorage     bool            `json:"enable_storage"`   // Se deve fazer upload para storage
	StorageType       string          `json:"storage_type"`     // Tipo de storage (local, r2)
	ProcessInParallel bool            `json:"process_parallel"` // Se deve processar qualidades em paralelo
}

// VideoJob representa um job de processamento de vídeo
type VideoJob struct {
	ID          string                 `json:"id"`
	Status      JobStatus              `json:"status"`
	Priority    JobPriority            `json:"priority"`
	InputFile   string                 `json:"input_file"`   // Caminho do arquivo de entrada
	OutputDir   string                 `json:"output_dir"`   // Diretório de saída
	Config      *VideoProcessingConfig `json:"config"`       // Configurações de processamento
	Metadata    *JobMetadata           `json:"metadata"`     // Metadados do job
	Result      *JobResult             `json:"result"`       // Resultado do processamento
	Error       string                 `json:"error"`        // Mensagem de erro se houver
	CreatedAt   time.Time              `json:"created_at"`   // Timestamp de criação
	UpdatedAt   time.Time              `json:"updated_at"`   // Timestamp da última atualização
	StartedAt   *time.Time             `json:"started_at"`   // Timestamp de início do processamento
	CompletedAt *time.Time             `json:"completed_at"` // Timestamp de conclusão
}

// JobMetadata contém metadados adicionais do job
type JobMetadata struct {
	WorkerID     string            `json:"worker_id"`     // ID do worker que processou
	Attempts     int               `json:"attempts"`      // Número de tentativas
	MaxAttempts  int               `json:"max_attempts"`  // Máximo de tentativas permitidas
	RetryDelay   time.Duration     `json:"retry_delay"`   // Delay entre tentativas
	Timeout      time.Duration     `json:"timeout"`       // Timeout do job
	Tags         map[string]string `json:"tags"`          // Tags personalizadas
	ClientIP     string            `json:"client_ip"`     // IP do cliente que enviou
	UserAgent    string            `json:"user_agent"`    // User agent do cliente
	OriginalName string            `json:"original_name"` // Nome original do arquivo
	FileSize     int64             `json:"file_size"`     // Tamanho do arquivo em bytes
	ContentType  string            `json:"content_type"`  // Tipo MIME do arquivo
}

// QualityResult contém o resultado do processamento de uma qualidade específica
type QualityResult struct {
	Quality        string        `json:"quality"`         // Nome da qualidade (480p, 720p, 1080p)
	Success        bool          `json:"success"`         // Se o processamento foi bem-sucedido
	PlaylistURL    string        `json:"playlist_url"`    // URL do arquivo .m3u8 da qualidade
	PlaylistPath   string        `json:"playlist_path"`   // Caminho local do .m3u8 da qualidade
	SegmentURLs    []string      `json:"segment_urls"`    // URLs dos segmentos .ts da qualidade
	SegmentPaths   []string      `json:"segment_paths"`   // Caminhos locais dos segmentos da qualidade
	ProcessingTime time.Duration `json:"processing_time"` // Tempo de processamento da qualidade
	FileSize       int64         `json:"file_size"`       // Tamanho dos arquivos da qualidade
	SegmentCount   int           `json:"segment_count"`   // Número de segmentos da qualidade
	Error          string        `json:"error,omitempty"` // Erro específico da qualidade
}

// JobResult contém o resultado do processamento
type JobResult struct {
	Success            bool                   `json:"success"`
	MasterPlaylistURL  string                 `json:"master_playlist_url"`  // URL do arquivo master.m3u8
	MasterPlaylistPath string                 `json:"master_playlist_path"` // Caminho local do master.m3u8
	PlaylistURL        string                 `json:"playlist_url"`         // URL do arquivo .m3u8 (deprecated)
	PlaylistPath       string                 `json:"playlist_path"`        // Caminho local do .m3u8 (deprecated)
	SegmentURLs        []string               `json:"segment_urls"`         // URLs dos segmentos .ts (deprecated)
	SegmentPaths       []string               `json:"segment_paths"`        // Caminhos locais dos segmentos (deprecated)
	QualityResults     []QualityResult        `json:"quality_results"`      // Resultados por qualidade
	Duration           float64                `json:"duration"`             // Duração do vídeo em segundos
	ProcessingTime     time.Duration          `json:"processing_time"`      // Tempo total de processamento
	FileSize           int64                  `json:"file_size"`            // Tamanho total dos arquivos gerados
	SegmentCount       int                    `json:"segment_count"`        // Número total de segmentos gerados
	StorageUploaded    bool                   `json:"storage_uploaded"`     // Se foi feito upload para storage
	StorageProvider    string                 `json:"storage_provider"`     // Provedor de storage usado
	Metrics            map[string]interface{} `json:"metrics"`              // Métricas adicionais
}

// NewVideoJob cria um novo job de processamento de vídeo
func NewVideoJob(inputFile, outputDir string, config *VideoProcessingConfig) *VideoJob {
	now := time.Now()

	if config == nil {
		config = &VideoProcessingConfig{
			SegmentTime:       10,
			PlaylistType:      "vod",
			VideoCodec:        "libx264",
			AudioCodec:        "aac",
			VideoBitrates:     []int{1000, 2000, 4000}, // Mantido para compatibilidade
			Qualities:         getDefaultQualities(),
			EnableStorage:     true,
			StorageType:       "local",
			ProcessInParallel: true,
		}
	}

	return &VideoJob{
		ID:        generateJobID(),
		Status:    JobStatusPending,
		Priority:  PriorityNormal,
		InputFile: inputFile,
		OutputDir: outputDir,
		Config:    config,
		Metadata: &JobMetadata{
			Attempts:    0,
			MaxAttempts: 3,
			RetryDelay:  30 * time.Second,
			Timeout:     300 * time.Second, // 5 minutos
			Tags:        make(map[string]string),
		},
		Result:    &JobResult{},
		CreatedAt: now,
		UpdatedAt: now,
	}
}

// ToJSON converte o job para JSON
func (j *VideoJob) ToJSON() ([]byte, error) {
	return json.Marshal(j)
}

// FromJSON cria um job a partir de JSON
func FromJSON(data []byte) (*VideoJob, error) {
	var job VideoJob
	err := json.Unmarshal(data, &job)
	return &job, err
}

// UpdateStatus atualiza o status do job
func (j *VideoJob) UpdateStatus(status JobStatus) {
	j.Status = status
	j.UpdatedAt = time.Now()

	switch status {
	case JobStatusProcessing:
		now := time.Now()
		j.StartedAt = &now
	case JobStatusCompleted, JobStatusFailed, JobStatusCancelled:
		now := time.Now()
		j.CompletedAt = &now
	}
}

// IncrementAttempts incrementa o contador de tentativas
func (j *VideoJob) IncrementAttempts() {
	j.Metadata.Attempts++
	j.UpdatedAt = time.Now()
}

// CanRetry verifica se o job pode ser reprocessado
func (j *VideoJob) CanRetry() bool {
	return j.Metadata.Attempts < j.Metadata.MaxAttempts
}

// IsExpired verifica se o job expirou
func (j *VideoJob) IsExpired() bool {
	if j.StartedAt == nil {
		return false
	}
	return time.Since(*j.StartedAt) > j.Metadata.Timeout
}

// SetError define uma mensagem de erro
func (j *VideoJob) SetError(err error) {
	if err != nil {
		j.Error = err.Error()
	}
	j.UpdatedAt = time.Now()
}

// SetWorker define o worker que está processando o job
func (j *VideoJob) SetWorker(workerID string) {
	j.Metadata.WorkerID = workerID
	j.UpdatedAt = time.Now()
}

// GetQueueKey retorna a chave da fila baseada na prioridade
func (j *VideoJob) GetQueueKey() string {
	switch j.Priority {
	case PriorityHigh:
		return "video_processing:high"
	case PriorityLow:
		return "video_processing:low"
	default:
		return "video_processing:normal"
	}
}

// generateJobID gera um ID único para o job
func generateJobID() string {
	bytes := make([]byte, 16)
	if _, err := rand.Read(bytes); err != nil {
		// Fallback para timestamp se falhar
		return fmt.Sprintf("job_%d", time.Now().UnixNano())
	}
	return fmt.Sprintf("job_%s", hex.EncodeToString(bytes))
}

// getDefaultQualities retorna as configurações padrão de qualidades
func getDefaultQualities() []QualityConfig {
	return []QualityConfig{
		{
			Name:      "480p",
			Width:     854,
			Height:    480,
			Bitrate:   1000, // 1 Mbps
			AudioRate: 128,  // 128 kbps
			MaxRate:   1200, // 1.2 Mbps
			BufSize:   2000, // 2 Mbps
		},
		{
			Name:      "720p",
			Width:     1280,
			Height:    720,
			Bitrate:   2500, // 2.5 Mbps
			AudioRate: 128,  // 128 kbps
			MaxRate:   3000, // 3 Mbps
			BufSize:   5000, // 5 Mbps
		},
		{
			Name:      "1080p",
			Width:     1920,
			Height:    1080,
			Bitrate:   5000,  // 5 Mbps
			AudioRate: 192,   // 192 kbps
			MaxRate:   6000,  // 6 Mbps
			BufSize:   10000, // 10 Mbps
		},
	}
}
