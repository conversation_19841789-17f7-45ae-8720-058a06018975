# Configurações básicas do servidor
PORT=8080
ENVIRONMENT=development
VIDEO_DIR=./videos
MAX_FILE_SIZE=104857600  # 100MB

# Configuração FFmpeg
ENABLE_FFMPEG=true

# Configurações de Autenticação
ENABLE_AUTH=false  # true para habilitar autenticação com API key
API_KEYS=your-secret-api-key-1,your-secret-api-key-2  # Lista de API keys válidas separadas por vírgula

# Configurações de Storage
ENABLE_STORAGE=true
STORAGE_TYPE=local  # local ou r2

# Configurações para armazenamento local (desenvolvimento)
LOCAL_STORAGE_DIR=./storage
LOCAL_STORAGE_URL=/files

# Configurações Cloudflare R2 (produção)
# Obtenha essas informações no painel do Cloudflare R2
R2_ENDPOINT=https://your-account-id.r2.cloudflarestorage.com
R2_ACCESS_KEY=your-r2-access-key
R2_SECRET_KEY=your-r2-secret-key
R2_BUCKET_NAME=your-bucket-name
R2_PUBLIC_URL=https://your-custom-domain.com  # Opcional: URL personalizada para servir arquivos

# Exemplo de configuração R2 completa:
# R2_ENDPOINT=https://abc123def456.r2.cloudflarestorage.com
# R2_ACCESS_KEY=1234567890abcdef1234567890abcdef
# R2_SECRET_KEY=abcdef1234567890abcdef1234567890abcdef12
# R2_BUCKET_NAME=apexstream-videos
# R2_PUBLIC_URL=https://videos.meudominio.com
